<!-- 客户表单（用于模态框） -->
<style>
/* 标签显示容器样式 */
.tag-display-container {
    min-height: 40px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 8px;
    background-color: #f8f9fa;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: flex-start;
}

/* 标签样式 */
.tag {
    display: inline-flex;
    align-items: center;
    background-color: #0d6efd;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin: 2px;
    white-space: nowrap;
    animation: fadeIn 0.3s ease-in;
}

.tag .remove-tag {
    margin-left: 5px;
    cursor: pointer;
    font-weight: bold;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.tag .remove-tag:hover {
    opacity: 1;
    color: #ffcccc;
}

/* 空状态提示 */
.tag-display-container.empty::before {
    content: "暂无标签";
    color: #6c757d;
    font-style: italic;
    font-size: 14px;
}

.tag-display-container:not(.empty)::before {
    display: none;
}

/* 标签淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 输入框聚焦时的样式 */
.tag-input-focused .tag-display-container {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}
</style>

<form method="post" id="client-form" data-ajax-form
      {% if edit_mode %}data-edit-mode="true" data-client-id="{{ client.id }}"{% endif %}>
    {{ form.csrf_token }}
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.name.label(class="form-label") }}
                {{ form.name(class="form-control", placeholder="请输入客户名称") }}
                {% if form.name.errors %}
                    {% for error in form.name.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.contact.label(class="form-label") }}
                {{ form.contact(class="form-control", placeholder="请输入联系人") }}
                {% if form.contact.errors %}
                    {% for error in form.contact.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.phone.label(class="form-label") }}
                {{ form.phone(class="form-control", placeholder="请输入联系电话") }}
                {% if form.phone.errors %}
                    {% for error in form.phone.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.email.label(class="form-label") }}
                {{ form.email(class="form-control", placeholder="请输入电子邮箱") }}
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.need_review.label(class="form-label") }}
                <div class="form-check form-switch">
                    {{ form.need_review(class="form-check-input", id="need_review_switch") }}
                    <label class="form-check-label" for="need_review_switch">
                        <span id="need_review_text">{{ '需要审核' if form.need_review.data else '无需审核' }}</span>
                    </label>
                </div>
                {% if form.need_review.errors %}
                    {% for error in form.need_review.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.status.label(class="form-label") }}
                <div class="form-check form-switch">
                    {{ form.status(class="form-check-input", id="status_switch") }}
                    <label class="form-check-label" for="status_switch">
                        <span id="status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                    </label>
                </div>
                {% if form.status.errors %}
                    {% for error in form.status.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                {{ form.daily_content_count.label(class="form-label") }}
                {{ form.daily_content_count(class="form-control", min="1", max="100") }}
                {% if form.daily_content_count.errors %}
                    {% for error in form.daily_content_count.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                {{ form.interval_min.label(class="form-label") }}
                {{ form.interval_min(class="form-control", min="1", max="1440") }}
                {% if form.interval_min.errors %}
                    {% for error in form.interval_min.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                {{ form.interval_max.label(class="form-label") }}
                {{ form.interval_max(class="form-control", min="1", max="1440") }}
                {% if form.interval_max.errors %}
                    {% for error in form.interval_max.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.display_start_time.label(class="form-label") }}
                {{ form.display_start_time(class="form-control") }}
                {% if form.display_start_time.errors %}
                    {% for error in form.display_start_time.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.address.label(class="form-label") }}
                {{ form.address(class="form-control", placeholder="请输入地址") }}
                {% if form.address.errors %}
                    {% for error in form.address.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 审核超时设置 -->
    <div class="card mb-3">
        <div class="card-header bg-light py-2">
            <h6 class="card-title mb-0">
                <i class="bi bi-clock-history"></i> 审核超时设置
            </h6>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="form-check form-switch">
                        {{ form.auto_approve_enabled(class="form-check-input", id="auto_approve_switch") }}
                        <label class="form-check-label" for="auto_approve_switch">
                            {{ form.auto_approve_enabled.label.text }}
                        </label>
                        <small class="form-text text-muted d-block">启用后，客户审核超时将自动通过</small>
                    </div>
                    {% if form.auto_approve_enabled.errors %}
                        {% for error in form.auto_approve_enabled.errors %}
                        <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.review_timeout_hours.label(class="form-label") }}
                        {{ form.review_timeout_hours(class="form-control", min="1", max="168") }}
                        {% if form.review_timeout_hours.errors %}
                            {% for error in form.review_timeout_hours.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <small class="form-text text-muted">从进入客户审核状态开始计算</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.review_deadline_time.label(class="form-label") }}
                        {{ form.review_deadline_time(class="form-control") }}
                        {% if form.review_deadline_time.errors %}
                            {% for error in form.review_deadline_time.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <small class="form-text text-muted">每天到达此时间自动通过</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        {{ form.remark.label(class="form-label") }}
        {{ form.remark(class="form-control", rows="3", placeholder="请输入备注信息") }}
        {% if form.remark.errors %}
            {% for error in form.remark.errors %}
            <div class="invalid-feedback d-block">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- 默认值设置 -->
    <hr>
    <h6 class="text-primary"><i class="bi bi-gear"></i> 内容生成默认值设置</h6>
    <p class="text-muted small">设置该客户的默认话题、@用户和定位信息，选择客户后会自动填充到内容生成页面</p>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">默认必选话题</label>
                <input type="text"
                       class="form-control"
                       id="default-required-topics-input"
                       placeholder="输入话题后回车添加，支持多行粘贴"
                       data-container="default-required-topics-container"
                       data-field="default_required_topics">
                <div class="tag-display-container mt-2" id="default-required-topics-container">
                    <!-- 标签将在这里显示 -->
                </div>
                <div class="form-text">每行一个话题，生成内容时会自动添加到必选话题中</div>
                <!-- 隐藏字段存储数据 -->
                {{ form.default_required_topics(style="display: none;") }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">默认随机话题</label>
                <input type="text"
                       class="form-control"
                       id="default-random-topics-input"
                       placeholder="输入话题后回车添加，支持多行粘贴"
                       data-container="default-random-topics-container"
                       data-field="default_random_topics">
                <div class="tag-display-container mt-2" id="default-random-topics-container">
                    <!-- 标签将在这里显示 -->
                </div>
                <div class="form-text">每行一个话题，生成内容时会自动添加到随机话题中</div>
                <!-- 隐藏字段存储数据 -->
                {{ form.default_random_topics(style="display: none;") }}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">默认@用户</label>
                <input type="text"
                       class="form-control"
                       id="default-at-users-input"
                       placeholder="输入用户名后回车添加，支持多行粘贴"
                       data-container="default-at-users-container"
                       data-field="default_at_users">
                <div class="tag-display-container mt-2" id="default-at-users-container">
                    <!-- 标签将在这里显示 -->
                </div>
                <div class="form-text">每行一个用户，可以带@符号或不带，生成内容时会自动添加</div>
                <!-- 隐藏字段存储数据 -->
                {{ form.default_at_users(style="display: none;") }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">默认定位信息</label>
                <input type="text"
                       class="form-control"
                       id="default-location-input"
                       placeholder="输入定位信息后回车添加，支持多行粘贴"
                       data-container="default-location-container"
                       data-field="default_location">
                <div class="tag-display-container mt-2" id="default-location-container">
                    <!-- 标签将在这里显示 -->
                </div>
                <div class="form-text">每行一个定位信息，生成内容时会随机选择其中一个</div>
                <!-- 隐藏字段存储数据 -->
                {{ form.default_location(style="display: none;") }}
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle"></i> 取消
        </button>
        <button type="button" class="btn btn-primary" id="submit-btn">
            <i class="bi bi-check-circle"></i> {% if edit_mode %}更新{% else %}保存{% endif %}
        </button>
    </div>
</form>

<script>
// 客户表单标签输入功能 - 参考内容生成页面的实现
(function() {
    'use strict';

    console.log('🚀 开始初始化客户表单标签输入功能...');

    // 通用添加标签函数 - 参考内容生成页面
    function addTag(value, containerId, hiddenFieldId) {
        console.log('📝 addTag 调用:', { value, containerId, hiddenFieldId });

        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container) {
            console.error('❌ 找不到容器:', containerId);
            return false;
        }

        if (!hiddenField) {
            console.error('❌ 找不到隐藏字段:', hiddenFieldId);
            return false;
        }

        // 检查是否已存在相同标签
        const existingTags = container.querySelectorAll('.tag');
        for (let i = 0; i < existingTags.length; i++) {
            const existingText = existingTags[i].textContent.trim().replace('×', '').trim();
            if (existingText === value) {
                console.log('⚠️ 标签已存在，不重复添加:', value);
                return false;
            }
        }

        // 创建新标签
        const tag = document.createElement('span');
        tag.className = 'tag';
        tag.innerHTML = `${value} <span class="remove" onclick="removeClientTag(this, '${hiddenFieldId}')">×</span>`;

        // 添加到容器
        container.appendChild(tag);
        updateContainerEmptyState(container);

        // 更新隐藏字段
        updateHiddenField(containerId, hiddenFieldId);

        console.log('✅ 标签添加成功:', value);
        return true;
    }

    // 更新容器空状态
    function updateContainerEmptyState(container) {
        const tags = container.querySelectorAll('.tag');
        if (tags.length === 0) {
            container.classList.add('empty');
        } else {
            container.classList.remove('empty');
        }
    }

    // 更新隐藏字段 - 参考内容生成页面
    function updateHiddenField(containerId, hiddenFieldId) {
        console.log('🔄 updateHiddenField:', { containerId, hiddenFieldId });

        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container || !hiddenField) {
            console.error('❌ 找不到容器或隐藏字段:', { containerId, hiddenFieldId });
            return;
        }

        const tags = container.querySelectorAll('.tag');
        const values = [];

        tags.forEach(tag => {
            const text = tag.textContent.replace('×', '').trim();
            if (text) {
                values.push(text);
            }
        });

        // 所有字段都支持多个值，用换行符分隔
        hiddenField.value = values.join('\n');

        console.log('💾 隐藏字段已更新:', hiddenFieldId, '=', hiddenField.value);
    }

    // 删除标签函数 - 全局函数，供onclick调用
    window.removeClientTag = function(removeBtn, hiddenFieldId) {
        console.log('🗑️ removeClientTag 调用:', hiddenFieldId);

        const tag = removeBtn.parentElement;
        const container = tag.parentElement;
        const containerId = container.id;

        tag.remove();
        updateContainerEmptyState(container);
        updateHiddenField(containerId, hiddenFieldId);
    };

    // 添加必选话题
    function addDefaultRequiredTopic() {
        const input = document.getElementById('default-required-topics-input');
        const value = input.value.trim();

        if (value) {
            addTag(value, 'default-required-topics-container', 'default_required_topics');
            input.value = '';
        }
    }

    // 添加随机话题
    function addDefaultRandomTopic() {
        const input = document.getElementById('default-random-topics-input');
        const value = input.value.trim();

        if (value) {
            addTag(value, 'default-random-topics-container', 'default_random_topics');
            input.value = '';
        }
    }

    // 添加@用户
    function addDefaultAtUser() {
        const input = document.getElementById('default-at-users-input');
        let value = input.value.trim();

        if (value) {
            // 确保有@符号
            if (!value.startsWith('@')) {
                value = '@' + value;
            }
            addTag(value, 'default-at-users-container', 'default_at_users');
            input.value = '';
        }
    }

    // 添加定位信息
    function addDefaultLocation() {
        const input = document.getElementById('default-location-input');
        const value = input.value.trim();

        if (value) {
            addTag(value, 'default-location-container', 'default_location');
            input.value = '';
        }
    }

    // 初始化标签输入 - 参考内容生成页面的实现
    function initTagInputs() {
        console.log('🔧 开始初始化标签输入...');

        // 必选话题输入
        const requiredTopicInput = document.getElementById('default-required-topics-input');
        if (requiredTopicInput) {
            console.log('🔧 初始化必选话题输入');

            // 键盘事件 - 回车添加
            requiredTopicInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ 必选话题回车事件');
                    addDefaultRequiredTopic();
                }
            });

            // 失去焦点事件 - 自动添加
            requiredTopicInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    console.log('👁️ 必选话题失焦事件');
                    addDefaultRequiredTopic();
                }
            });

            // 粘贴事件
            requiredTopicInput.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);

                lines.forEach(line => {
                    addTag(line, 'default-required-topics-container', 'default_required_topics');
                });
                this.value = '';
            });
        }

        // 随机话题输入
        const randomTopicInput = document.getElementById('default-random-topics-input');
        if (randomTopicInput) {
            console.log('🔧 初始化随机话题输入');

            randomTopicInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ 随机话题回车事件');
                    addDefaultRandomTopic();
                }
            });

            randomTopicInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    console.log('👁️ 随机话题失焦事件');
                    addDefaultRandomTopic();
                }
            });

            randomTopicInput.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);

                lines.forEach(line => {
                    addTag(line, 'default-random-topics-container', 'default_random_topics');
                });
                this.value = '';
            });
        }

        // @用户输入
        const atUserInput = document.getElementById('default-at-users-input');
        if (atUserInput) {
            console.log('🔧 初始化@用户输入');

            atUserInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ @用户回车事件');
                    addDefaultAtUser();
                }
            });

            atUserInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    console.log('👁️ @用户失焦事件');
                    addDefaultAtUser();
                }
            });

            atUserInput.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);

                lines.forEach(line => {
                    let value = line;
                    if (!value.startsWith('@')) {
                        value = '@' + value;
                    }
                    addTag(value, 'default-at-users-container', 'default_at_users');
                });
                this.value = '';
            });
        }

        // 定位信息输入
        const locationInput = document.getElementById('default-location-input');
        if (locationInput) {
            console.log('🔧 初始化定位信息输入');

            locationInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ 定位信息回车事件');
                    addDefaultLocation();
                }
            });

            locationInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    console.log('👁️ 定位信息失焦事件');
                    addDefaultLocation();
                }
            });

            // 粘贴事件
            locationInput.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);

                console.log('📋 定位信息粘贴事件:', '行数:', lines.length);

                // 支持多个定位信息，每行作为一个标签
                lines.forEach(line => {
                    addTag(line, 'default-location-container', 'default_location');
                });
                this.value = '';
            });
        }

        console.log('✅ 标签输入初始化完成');
    }

    // 从隐藏字段加载现有数据
    function loadExistingData() {
        console.log('📂 开始加载现有数据...');

        const mappings = [
            { hiddenId: 'default_required_topics', containerId: 'default-required-topics-container' },
            { hiddenId: 'default_random_topics', containerId: 'default-random-topics-container' },
            { hiddenId: 'default_at_users', containerId: 'default-at-users-container' },
            { hiddenId: 'default_location', containerId: 'default-location-container' }
        ];

        mappings.forEach(mapping => {
            const hiddenField = document.getElementById(mapping.hiddenId);
            const container = document.getElementById(mapping.containerId);

            if (hiddenField && container) {
                const value = hiddenField.value;
                console.log('📂 加载字段数据:', mapping.hiddenId, '=', value);

                if (value) {
                    const values = value.split('\n').map(v => v.trim()).filter(v => v);
                    console.log('📂 分割后的值:', values);

                    values.forEach(val => {
                        // 使用addTag函数而不是addTagToContainer
                        addTag(val, mapping.containerId, mapping.hiddenId);
                    });
                    updateContainerEmptyState(container);
                }
            } else {
                console.warn('⚠️ 找不到字段或容器:', mapping);
            }
        });

        console.log('📂 现有数据加载完成');
    }

    // 延迟初始化，确保DOM完全加载
    function delayedInit() {
        console.log('⏰ 延迟初始化开始...');

        // 检查关键元素是否存在
        const requiredElements = [
            'default-required-topics-input',
            'default-required-topics-container',
            'default_required_topics'
        ];

        const allExists = requiredElements.every(id => {
            const exists = !!document.getElementById(id);
            console.log('🔍 检查元素:', id, exists ? '✅' : '❌');
            return exists;
        });

        if (allExists) {
            initTagInputs();
            loadExistingData();
            console.log('✅ 客户表单标签输入功能初始化完成！');
        } else {
            console.error('❌ 关键元素缺失，初始化失败');
            // 再次尝试
            setTimeout(delayedInit, 1000);
        }
    }

    // 多种方式确保初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', delayedInit);
    } else {
        setTimeout(delayedInit, 100);
    }

    // 额外的延迟初始化，防止动态加载问题
    setTimeout(delayedInit, 500);
    setTimeout(delayedInit, 1500); // 再加一个更长的延迟

})();
</script>

<!-- JavaScript已移至主页面处理，因为动态加载的script标签不会执行 -->
