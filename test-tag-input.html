<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签输入功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* 标签显示容器样式 */
        .tag-display-container {
            min-height: 40px;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 8px;
            background-color: #f8f9fa;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: flex-start;
        }

        .tag-input-focused .tag-display-container {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            background-color: #0d6efd;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
            white-space: nowrap;
        }

        .tag .remove-tag {
            margin-left: 5px;
            cursor: pointer;
            font-weight: bold;
            opacity: 0.7;
        }

        .tag .remove-tag:hover {
            opacity: 1;
        }

        /* 空状态提示 */
        .tag-display-container.empty::before {
            content: "暂无标签";
            color: #6c757d;
            font-style: italic;
            font-size: 14px;
        }

        .tag-display-container:not(.empty)::before {
            display: none;
        }
        
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🏷️ 标签输入功能测试</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>功能说明</h5>
            </div>
            <div class="card-body">
                <ul>
                    <li>✅ 输入内容后按回车添加标签</li>
                    <li>✅ 输入框失去焦点时自动添加标签</li>
                    <li>✅ 支持粘贴多行内容，按换行分割成多个标签</li>
                    <li>✅ 点击标签的×号删除标签</li>
                    <li>✅ 点击容器区域聚焦到输入框</li>
                    <li>✅ @用户自动添加@符号</li>
                </ul>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">必选话题</div>
                    <div class="card-body">
                        <input type="text"
                               class="form-control"
                               id="required-topics-input"
                               placeholder="输入话题后回车添加，支持多行粘贴"
                               data-container="required-topics-container">
                        <div class="tag-display-container empty mt-2" id="required-topics-container">
                            <!-- 标签将在这里显示 -->
                        </div>
                        <input type="hidden" id="required_topics">
                        <div class="test-result mt-2" id="required-topics-result">隐藏字段值将显示在这里</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">随机话题</div>
                    <div class="card-body">
                        <input type="text"
                               class="form-control"
                               id="random-topics-input"
                               placeholder="输入话题后回车添加，支持多行粘贴"
                               data-container="random-topics-container">
                        <div class="tag-display-container empty mt-2" id="random-topics-container">
                            <!-- 标签将在这里显示 -->
                        </div>
                        <input type="hidden" id="random_topics">
                        <div class="test-result mt-2" id="random-topics-result">隐藏字段值将显示在这里</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">@用户</div>
                    <div class="card-body">
                        <input type="text"
                               class="form-control"
                               id="at-users-input"
                               placeholder="输入用户名后回车添加，支持多行粘贴"
                               data-container="at-users-container">
                        <div class="tag-display-container empty mt-2" id="at-users-container">
                            <!-- 标签将在这里显示 -->
                        </div>
                        <input type="hidden" id="at_users">
                        <div class="test-result mt-2" id="at-users-result">隐藏字段值将显示在这里</div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">定位信息</div>
                    <div class="card-body">
                        <input type="text"
                               class="form-control"
                               id="location-input"
                               placeholder="输入定位信息后回车添加"
                               data-container="location-container">
                        <div class="tag-display-container empty mt-2" id="location-container">
                            <!-- 标签将在这里显示 -->
                        </div>
                        <input type="hidden" id="location">
                        <div class="test-result mt-2" id="location-result">隐藏字段值将显示在这里</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>测试数据</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="loadTestData()">加载测试数据</button>
                <button class="btn btn-secondary me-2" onclick="clearAllData()">清空所有数据</button>
                <button class="btn btn-info" onclick="exportData()">导出数据</button>
                
                <div class="mt-3">
                    <h6>测试用多行粘贴数据：</h6>
                    <textarea class="form-control" rows="3" readonly>健康餐
减脂美食
营养搭配
低卡料理</textarea>
                    <small class="text-muted">复制上面的内容，粘贴到话题输入框中测试</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签输入功能实现
        (function() {
            'use strict';
            
            // 添加标签到容器
            function addTagToContainer(container, text) {
                if (!text || text.trim() === '') return false;
                
                text = text.trim();
                
                // 检查是否已存在
                const existingTags = container.querySelectorAll('.tag');
                for (let tag of existingTags) {
                    if (tag.textContent.replace('×', '').trim() === text) {
                        return false; // 已存在，不添加
                    }
                }
                
                // 创建标签元素
                const tagElement = document.createElement('span');
                tagElement.className = 'tag';
                tagElement.innerHTML = `${text} <span class="remove-tag">×</span>`;
                
                // 添加删除事件
                tagElement.querySelector('.remove-tag').addEventListener('click', function() {
                    tagElement.remove();
                    updateContainerEmptyState(container);
                    updateHiddenFieldFromContainer(container);
                });
                
                container.appendChild(tagElement);
                updateContainerEmptyState(container);
                return true;
            }
            
            // 更新容器空状态
            function updateContainerEmptyState(container) {
                const tags = container.querySelectorAll('.tag');
                if (tags.length === 0) {
                    container.classList.add('empty');
                } else {
                    container.classList.remove('empty');
                }
            }
            
            // 从容器更新隐藏字段
            function updateHiddenFieldFromContainer(container) {
                const containerId = container.id;
                let hiddenFieldId = '';
                
                // 确定对应的隐藏字段
                switch(containerId) {
                    case 'required-topics-container':
                        hiddenFieldId = 'required_topics';
                        break;
                    case 'random-topics-container':
                        hiddenFieldId = 'random_topics';
                        break;
                    case 'at-users-container':
                        hiddenFieldId = 'at_users';
                        break;
                    case 'location-container':
                        hiddenFieldId = 'location';
                        break;
                }
                
                if (hiddenFieldId) {
                    const hiddenField = document.getElementById(hiddenFieldId);
                    if (hiddenField) {
                        const tags = container.querySelectorAll('.tag');
                        const values = [];
                        tags.forEach(tag => {
                            const text = tag.textContent.replace('×', '').trim();
                            if (text) {
                                values.push(text);
                            }
                        });
                        
                        // 对于定位信息，只取第一个值
                        if (containerId === 'location-container') {
                            hiddenField.value = values.length > 0 ? values[0] : '';
                        } else {
                            hiddenField.value = values.join('\n');
                        }
                        
                        // 更新显示
                        const resultDiv = document.getElementById(containerId.replace('-container', '-result'));
                        if (resultDiv) {
                            resultDiv.textContent = hiddenField.value || '(空)';
                        }
                    }
                }
            }
            
            // 初始化标签输入
            function initializeTagInputs() {
                const inputs = [
                    'required-topics-input',
                    'random-topics-input', 
                    'at-users-input',
                    'location-input'
                ];
                
                inputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    if (!input) return;
                    
                    const containerId = input.getAttribute('data-container');
                    const container = document.getElementById(containerId);
                    if (!container) return;
                    
                    // 初始化空状态
                    updateContainerEmptyState(container);
                    
                    // 回车事件
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            const text = input.value.trim();
                            if (text) {
                                // 处理@用户，确保有@符号
                                let processedText = text;
                                if (containerId === 'at-users-container' && !text.startsWith('@')) {
                                    processedText = '@' + text;
                                }
                                
                                if (addTagToContainer(container, processedText)) {
                                    input.value = '';
                                    updateHiddenFieldFromContainer(container);
                                }
                            }
                        }
                    });
                    
                    // 失去焦点事件
                    input.addEventListener('blur', function() {
                        const text = input.value.trim();
                        if (text) {
                            // 处理@用户，确保有@符号
                            let processedText = text;
                            if (containerId === 'at-users-container' && !text.startsWith('@')) {
                                processedText = '@' + text;
                            }
                            
                            if (addTagToContainer(container, processedText)) {
                                input.value = '';
                                updateHiddenFieldFromContainer(container);
                            }
                        }
                    });
                    
                    // 粘贴事件
                    input.addEventListener('paste', function(e) {
                        e.preventDefault();
                        const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                        const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);
                        
                        lines.forEach(line => {
                            // 处理@用户，确保有@符号
                            let processedText = line;
                            if (containerId === 'at-users-container' && !line.startsWith('@')) {
                                processedText = '@' + line;
                            }
                            
                            addTagToContainer(container, processedText);
                        });
                        
                        updateHiddenFieldFromContainer(container);
                        input.value = '';
                    });
                    
                    // 容器点击事件，聚焦到输入框
                    container.addEventListener('click', function() {
                        input.focus();
                    });
                });
            }
            
            // 全局函数
            window.loadTestData = function() {
                const testData = {
                    'required-topics-container': ['健康餐', '减脂美食', '营养搭配'],
                    'random-topics-container': ['美食分享', '生活记录', '日常vlog'],
                    'at-users-container': ['@用户1', '@用户2', '@用户3'],
                    'location-container': ['北京市朝阳区']
                };
                
                Object.keys(testData).forEach(containerId => {
                    const container = document.getElementById(containerId);
                    if (container) {
                        // 清空现有标签
                        container.innerHTML = '';
                        
                        // 添加测试数据
                        testData[containerId].forEach(text => {
                            addTagToContainer(container, text);
                        });
                        
                        updateHiddenFieldFromContainer(container);
                    }
                });
            };
            
            window.clearAllData = function() {
                const containers = [
                    'required-topics-container',
                    'random-topics-container',
                    'at-users-container',
                    'location-container'
                ];
                
                containers.forEach(containerId => {
                    const container = document.getElementById(containerId);
                    if (container) {
                        container.innerHTML = '';
                        updateContainerEmptyState(container);
                        updateHiddenFieldFromContainer(container);
                    }
                });
            };
            
            window.exportData = function() {
                const data = {
                    required_topics: document.getElementById('required_topics').value,
                    random_topics: document.getElementById('random_topics').value,
                    at_users: document.getElementById('at_users').value,
                    location: document.getElementById('location').value
                };
                
                alert('导出数据：\n' + JSON.stringify(data, null, 2));
            };
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                initializeTagInputs();
                console.log('✅ 标签输入功能已初始化');
            });
            
        })();
    </script>
</body>
</html>
