<!-- 简化版内容生成页面 - 完整的批量生成功能 -->
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">批量生成文案</h2>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">文案生成设置</h5>
        </div>
        <div class="card-body">
            <form method="post" id="generateForm" data-ajax-form="true" onsubmit="return prepareFormSubmission()">
                {{ form.csrf_token }}
                <input type="hidden" name="form_validated" id="form_validated" value="0">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select", id="client_id", onchange="onClientChange(this)") }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.task_id.label(class="form-label") }}
                            {{ form.task_id(class="form-select", id="task_id") }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.new_task_name.label(class="form-label") }}
                            {{ form.new_task_name(class="form-control", id="new_task_name") }}
                            <small class="text-muted" id="task-name-hint">选择已有任务时显示任务名称，选择"创建新任务"时可编辑</small>

                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.batch_name.label(class="form-label") }}
                            {{ form.batch_name(class="form-control", id="batch_name", readonly=true) }}
                            <small class="text-muted">批次号自动生成，不可修改</small>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group">
                            {{ form.template_category_id.label(class="form-label") }}
                            {{ form.template_category_id(class="form-select", id="template_category_id") }}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <h5 class="mb-0 me-2">标记替换设置</h5>
                                        <span id="marks-info" class="badge bg-info me-2"></span>
                                        <button type="button" id="refreshMarksBtn" class="btn btn-outline-secondary btn-sm" title="刷新当前分类的标记">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新
                                        </button>
                                    </div>
                                    <span id="template-count" class="badge bg-primary">未选择模板分类</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="marks-container">
                                    <p class="text-muted">请先选择模板分类，系统将自动加载需要替换的标记</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 话题和@用户输入区域 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.required_topics.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">必选</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="required_topic_input" class="form-control form-control-sm" placeholder="输入话题后按回车添加，支持多行粘贴" autocomplete="off">
                                </div>
                            </div>
                            <div id="required-topics-container" class="tags-container mb-2"></div>
                            {{ form.required_topics(class="form-control d-none", id="required_topics", rows=5) }}
                            <small class="text-muted">可以批量粘贴话题，每行一个，或用空格分隔</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.random_topics.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">随机</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="random_topic_input" class="form-control form-control-sm" placeholder="输入话题后按回车添加，支持多行粘贴" autocomplete="off">
                                </div>
                            </div>
                            <div id="random-topics-container" class="tags-container mb-2"></div>
                            {{ form.random_topics(class="form-control d-none", id="random_topics", rows=5) }}
                            <small class="text-muted">可以批量粘贴话题，每行一个，或用空格分隔</small>
                        </div>
                    </div>
                </div>

                <!-- @用户输入区域 -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.at_users.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">@用户</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="at_user_input" class="form-control form-control-sm" placeholder="输入用户名后按回车添加，支持多行粘贴" autocomplete="off">
                                </div>
                            </div>
                            <div id="at-users-container" class="tags-container mb-2"></div>
                            {{ form.at_users(class="form-control d-none", id="at_users", rows=3) }}
                            <small class="text-muted">可以批量粘贴用户名，每行一个，或用空格分隔，不需要添加@符号</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.location.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">定位</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="location_input" class="form-control form-control-sm" placeholder="输入定位后按回车添加，支持多行粘贴" autocomplete="off">
                                </div>
                            </div>
                            <div id="locations-container" class="tags-container mb-2"></div>
                            {{ form.location(class="form-control d-none", id="location", rows=3) }}
                            <small class="text-muted">可以批量粘贴定位，每行一个，或用空格分隔</small>
                        </div>
                    </div>
                </div>

                <!-- 设置类选项 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">最大话题数量</label>
                            <input type="number" class="form-control" id="max_topics_count" name="max_topics_count" min="1" max="10" value="10">
                            <small class="text-muted">小红书最多允许10个话题</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">随机@用户数量</label>
                            <input type="number" class="form-control" id="random_at_users_count" name="random_at_users_count" min="0" max="10" value="1">
                            <small class="text-muted">每篇文章随机选择的@用户数量</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.publish_priority.label(class="form-label") }}
                            {{ form.publish_priority(class="form-select", id="publish_priority") }}
                        </div>
                    </div>
                </div>

                <!-- 生成设置区域 -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">生成设置</h5>
                    </div>
                    <div class="card-body">


                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.count.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.count(class="form-control", id="count") }}
                                        <button type="button" class="btn btn-outline-primary" id="validateCountBtn">检测</button>
                                    </div>
                                    <small class="text-muted">最多一次生成1000篇文案</small>
                                    <div id="count-feedback" class="mt-2"></div>
                                </div>

                                <!-- 重复性控制选项 - 垂直布局 -->
                                <div class="form-group mb-3">
                                    <label class="form-label fw-bold">重复性控制</label>
                                    <div class="duplicate-control-container">
                                        <div class="form-check-custom" data-value="client">
                                            <input class="form-check-input" type="radio" name="duplicate_control" id="client_no_duplicate" value="client">
                                            <label class="form-check-label-custom" for="client_no_duplicate">
                                                <span class="control-title">客户不重复</span>
                                                <small class="control-desc">该客户的所有文章中，每个模板只能使用一次</small>
                                            </label>
                                        </div>
                                        <div class="form-check-custom" data-value="task">
                                            <input class="form-check-input" type="radio" name="duplicate_control" id="task_no_duplicate" value="task" checked>
                                            <label class="form-check-label-custom" for="task_no_duplicate">
                                                <span class="control-title">任务不重复</span>
                                                <small class="control-desc">该任务下的所有文章中，每个模板只能使用一次</small>
                                            </label>
                                        </div>
                                        <div class="form-check-custom" data-value="batch">
                                            <input class="form-check-input" type="radio" name="duplicate_control" id="batch_no_duplicate" value="batch">
                                            <label class="form-check-label-custom" for="batch_no_duplicate">
                                                <span class="control-title">批次不重复</span>
                                                <small class="control-desc">该批次内的文章中，每个模板只能使用一次</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom-info-panel">
                                    <h6>生成预览</h6>
                                    <div id="generation-preview">
                                        <div class="preview-item">
                                            <span class="preview-label">可生成数量：</span>
                                            <span id="available-count" class="preview-value">-</span>
                                        </div>
                                        <div class="preview-item">
                                            <span class="preview-label">模板分类：</span>
                                            <span id="selected-category" class="preview-value">未选择</span>
                                        </div>
                                        <div class="preview-item">
                                            <span class="preview-label">模板总数：</span>
                                            <span id="total-templates" class="preview-value">0</span>
                                        </div>
                                        <div class="preview-item">
                                            <span class="preview-label">可用模板：</span>
                                            <span id="usable-templates" class="preview-value">0</span>
                                        </div>
                                        <div class="preview-item">
                                            <span class="preview-label">重复控制：</span>
                                            <span id="duplicate-mode" class="preview-value">任务不重复</span>
                                        </div>
                                        <div id="validation-warnings" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 隐藏的关键词字段，用于提交表单 -->
                <div class="d-none">
                    {{ form.keywords(id="keywords") }}
                    <input type="hidden" name="allow_template_duplicate" id="allow_template_duplicate" value="0">
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="custom-info-panel">
                            <h5>生成预览</h5>
                            <div id="generatePreview">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="loadPage('content-manage')">取消</button>
                            <button type="submit" class="btn btn-primary">生成文案</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        min-height: 36px;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 5px;
        background-color: #f8f9fa;
    }



    /* 标记名称标签样式 */
    .mark-label {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        background-color: #e9f5ff;
        color: #0066cc;
        border-radius: 4px;
        font-size: 0.875rem;
        border: 1px solid #b3d7ff;
        font-weight: bold;
        margin-right: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        white-space: nowrap;
    }

    /* 紧凑型表单组样式 */
    .compact-form-group {
        margin-bottom: 10px;
    }

    .compact-form-group .input-group {
        display: flex;
        align-items: center;
    }

    .compact-form-group .form-label {
        margin-bottom: 0.25rem;
        white-space: nowrap;
    }

    /* 标记输入区域样式 */
    .mark-input-container {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }

    /* 标记信息样式 */
    .marks-info {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    /* 确保所有标签不换行 */
    .form-label {
        white-space: nowrap;
    }

    /* 自定义信息面板样式 - 永不消失 */
    .custom-info-panel {
        min-height: 180px !important;
        padding: 1rem !important;
        margin-bottom: 1rem !important;
        background-color: #d1ecf1 !important;
        border: 1px solid #bee5eb !important;
        border-radius: 0.375rem !important;
        color: #0c5460 !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* 生成预览样式 */
    .preview-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding: 4px 0;
        border-bottom: 1px solid rgba(190, 229, 235, 0.5);
    }

    .preview-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .preview-label {
        font-weight: 500;
        color: #0c5460;
    }

    .preview-value {
        font-weight: 600;
        color: #0a4e5c;
    }

    .preview-value.warning {
        color: #dc3545;
        font-weight: bold;
    }

    .preview-value.success {
        color: #198754;
        font-weight: bold;
    }

    /* 重复性控制选项样式 */
    .duplicate-control-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .form-check-custom {
        padding: 12px 16px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
    }

    .form-check-custom:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-check-custom input[type="radio"] {
        position: absolute;
        top: 16px;
        left: 16px;
        margin: 0;
    }

    .form-check-label-custom {
        display: block;
        margin-left: 28px;
        cursor: pointer;
        width: 100%;
    }

    .control-title {
        display: block;
        font-weight: 600;
        color: #495057;
        margin-bottom: 4px;
    }

    .control-desc {
        display: block;
        color: #6c757d;
        font-size: 0.875rem;
        line-height: 1.3;
    }

    /* 选中状态样式 */
    .form-check-custom:has(input:checked) {
        background-color: #e7f3ff;
        border-color: #0d6efd;
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
    }

    .form-check-custom:has(input:checked) .control-title {
        color: #0d6efd;
        font-weight: 700;
    }

    .form-check-custom:has(input:checked) .control-desc {
        color: #495057;
    }

    /* 兼容性：如果浏览器不支持:has()，使用JavaScript添加的类 */
    .form-check-custom.selected {
        background-color: #e7f3ff;
        border-color: #0d6efd;
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
    }

    .form-check-custom.selected .control-title {
        color: #0d6efd;
        font-weight: 700;
    }

    .form-check-custom.selected .control-desc {
        color: #495057;
    }

    /* 验证警告样式 */
    #validation-warnings .alert {
        padding: 8px 12px;
        margin-bottom: 8px;
        font-size: 0.875rem;
    }

    #validation-warnings .alert:last-child {
        margin-bottom: 0;
    }

    /* 关键词标记状态样式 */
    .keyword-status-complete {
        color: #198754 !important;
        font-weight: 600;
    }

    .keyword-status-incomplete {
        color: #fd7e14 !important;
        font-weight: 600;
    }

    .keyword-status-missing {
        color: #dc3545 !important;
        font-weight: 600;
    }

    /* 刷新按钮旋转动画 */
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

<script>
    // 第一个DOMContentLoaded已删除，统一在下面的第二个DOMContentLoaded中初始化
</script>


</div>

<style>
/* 标记输入样式 */
.mark-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    width: 100%;
}

.mark-label {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.mark-input-container .input-group {
    flex: 1;
    min-width: 0;
}

.mark-input-container .form-control {
    width: 100%;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    min-height: 30px;
    padding: 5px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.tag {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.tag .remove {
    cursor: pointer;
    font-weight: bold;
    padding: 0 2px;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
}

.tag .remove:hover {
    background: rgba(255,255,255,0.5);
}
</style>

<script>
console.log('内容生成页面已加载 - 版本 2025-07-20-FIXED-MARKS');

// 立即测试模板分类选择器
const templateCategorySelect = document.getElementById('template_category_id');

if (templateCategorySelect) {
    console.log('找到模板分类选择器，立即添加事件监听器');
    templateCategorySelect.addEventListener('change', function() {
        console.log('=== 模板分类选择发生变化（立即版本）===');
        console.log('选择的值:', this.value);
        console.log('选择的文本:', this.options[this.selectedIndex].text);

        const categoryId = this.value;
        if (categoryId) {
            console.log('开始获取分类标记，分类ID:', categoryId);

            // 先清空标记容器
            const marksContainer = document.getElementById('marks-container');
            if (marksContainer) {
                marksContainer.innerHTML = '<p class="text-muted">正在加载标记...</p>';
                console.log('已清空标记容器，显示加载中');
            } else {
                console.error('找不到标记容器！');
                return;
            }

            // 获取该分类下的所有标记
            const url = `/simple/contents/get-marks/${categoryId}`;
            console.log('发送请求到:', url);

            fetch(url)
                .then(response => {
                    console.log('收到响应，状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('获取标记响应数据:', data);
                    console.log('标记列表:', data.marks);
                    console.log('模板数量:', data.template_count);

                    if (data.success) {
                        // 更新标记信息
                        const marksInfo = document.getElementById('marks-info');
                        if (marksInfo) {
                            marksInfo.textContent = data.marks && data.marks.length > 0
                                ? `共 ${data.marks.length} 个标记`
                                : '无标记';
                            console.log('已更新标记信息');
                        }

                        // 更新模板数量
                        const templateCount = document.getElementById('template-count');
                        if (templateCount) {
                            templateCount.textContent = data.template_count > 0
                                ? `共 ${data.template_count} 个模板`
                                : '无可用模板';
                            console.log('已更新模板数量');
                        }

                        // 清空标记容器
                        marksContainer.innerHTML = '';

                        if (data.marks && data.marks.length > 0) {
                            console.log('开始创建标记输入区域，标记数量:', data.marks.length);
                            // 创建标记输入区域
                            data.marks.forEach((mark, index) => {
                                console.log(`创建第${index + 1}个标记输入区域: ${mark}`);
                                createMarkInput(mark, marksContainer);
                            });
                            console.log('所有标记输入区域创建完成');
                        } else {
                            marksContainer.innerHTML = '<p class="text-muted">该分类下的模板没有需要替换的标记</p>';
                            console.log('该分类没有标记');
                        }
                    } else {
                        marksContainer.innerHTML = '<p class="text-danger">加载标记失败</p>';
                        console.error('服务器返回失败:', data);
                    }
                })
                .catch(error => {
                    console.error('获取标记失败:', error);
                    marksContainer.innerHTML = '<p class="text-danger">加载标记失败，请重试</p>';
                });
        } else {
            console.log('分类ID为空，清空标记容器');
            // 清空标记容器
            const marksContainer = document.getElementById('marks-container');
            if (marksContainer) {
                marksContainer.innerHTML = '<p class="text-muted">请先选择模板分类，系统将自动加载需要替换的标记</p>';
            }

            // 清空标记信息
            const marksInfo = document.getElementById('marks-info');
            if (marksInfo) marksInfo.textContent = '';

            const templateCount = document.getElementById('template-count');
            if (templateCount) templateCount.textContent = '未选择模板分类';
        }
    });
} else {
    console.error('立即检查：找不到模板分类选择器！');
    alert('错误：找不到模板分类选择器！');
}

// 延迟检查
setTimeout(function() {
    console.log('=== 延迟检查 ===');
    const templateCategorySelect2 = document.getElementById('template_category_id');
    const marksContainer = document.getElementById('marks-container');

    console.log('延迟检查 - 模板分类选择器:', templateCategorySelect2);
    console.log('延迟检查 - 标记容器:', marksContainer);

    if (templateCategorySelect2) {
        console.log('延迟检查 - 模板分类选择器选项数量:', templateCategorySelect2.options.length);
        console.log('延迟检查 - 模板分类选择器选项:', Array.from(templateCategorySelect2.options).map(opt => ({value: opt.value, text: opt.text})));
    } else {
        console.error('延迟检查：找不到模板分类选择器！');
    }
}, 1000);

// 简单测试：直接为任务选择添加事件监听
setTimeout(function() {
    const taskSelect = document.getElementById('task_id');
    if (taskSelect) {
        console.log('找到任务选择元素，当前值:', taskSelect.value);
        console.log('任务选择选项:', Array.from(taskSelect.options).map(opt => ({value: opt.value, text: opt.text})));

        taskSelect.addEventListener('change', function() {
            console.log('=== 任务选择变化 ===');
            console.log('新选择的值:', this.value);
            console.log('新选择的文本:', this.options[this.selectedIndex].text);

            const newTaskNameInput = document.getElementById('new_task_name');
            if (newTaskNameInput) {
                console.log('找到新任务名称输入框');
                if (this.value === '0') {
                    console.log('选择了创建新任务');
                    newTaskNameInput.readOnly = false;
                    newTaskNameInput.style.backgroundColor = '';
                    // 生成当天日期的任务名称
                    const now = new Date();
                    const defaultTaskName = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日任务`;
                    newTaskNameInput.value = defaultTaskName;
                } else {
                    console.log('选择了已有任务:', this.options[this.selectedIndex].text);
                    newTaskNameInput.readOnly = true;
                    newTaskNameInput.style.backgroundColor = '#e9ecef';
                    newTaskNameInput.value = this.options[this.selectedIndex].text;

                    // 查询该任务的批次数量并更新批次名称
                    const taskId = this.value;
                    fetch(`/simple/contents/get-batch-count/${taskId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const batchNameInput = document.getElementById('batch_name');
                                if (batchNameInput) {
                                    batchNameInput.value = `批次 ${data.next_batch_number}`;
                                    console.log(`设置批次名称为: 批次 ${data.next_batch_number}`);
                                }
                            }
                        })
                        .catch(error => {
                            console.error('获取批次数量失败:', error);
                        });
                }
            } else {
                console.error('找不到新任务名称输入框');
            }
        });
    } else {
        console.error('找不到任务选择元素');
    }
}, 1000);

// 立即执行的测试
console.log('JavaScript文件开始加载...');

// 全局初始化函数
window.initContentPageInputs = function() {
    console.log('=== 开始初始化内容页面输入框 ===');
    initSimpleInputs();
    console.log('=== 内容页面输入框初始化完成 ===');
};

// 简化版本：直接为4个输入框添加事件监听器
function initSimpleInputs() {
    console.log('开始初始化简单输入框...');

    // 为每个输入框添加事件
    var inputs = [
        {id: 'required_topic_input', container: 'required-topics-container', hidden: 'required_topics'},
        {id: 'random_topic_input', container: 'random-topics-container', hidden: 'random_topics'},
        {id: 'at_user_input', container: 'at-users-container', hidden: 'at_users'},
        {id: 'location_input', container: 'locations-container', hidden: 'location'}
    ];

    inputs.forEach(function(config) {
        var input = document.getElementById(config.id);
        if (input) {
            // 检查是否已经初始化过，避免重复绑定
            if (input.dataset.simpleInputInitialized) {
                console.log('输入框已初始化，跳过: ' + config.id);
                return;
            }

            console.log('找到输入框: ' + config.id);

            // 标记已初始化
            input.dataset.simpleInputInitialized = 'true';

            // 回车事件
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('回车键被按下: ' + config.id);
                    processSimpleInput(this, config.container, config.hidden);
                }
            });

            // 失去焦点事件
            input.addEventListener('blur', function() {
                console.log('失去焦点: ' + config.id);
                processSimpleInput(this, config.container, config.hidden);
            });

            // 粘贴事件 - 优化版本，避免重复处理
            input.addEventListener('paste', function(e) {
                console.log('🔄 检测到粘贴事件: ' + config.id);

                // 尝试直接从剪贴板获取数据
                if (e.clipboardData && e.clipboardData.getData) {
                    var pastedText = e.clipboardData.getData('text/plain');
                    console.log('📋 从剪贴板获取的文本: "' + pastedText + '"');

                    if (pastedText) {
                        e.preventDefault(); // 阻止默认粘贴行为

                        // 清空输入框
                        input.value = '';

                        // 直接处理剪贴板文本，不依赖输入框
                        processClipboardText(pastedText, config.container, config.hidden);
                        return;
                    }
                }

                // 如果无法直接获取剪贴板数据，使用延迟处理
                setTimeout(function() {
                    console.log('粘贴延迟处理开始');
                    processSimpleInput(input, config.container, config.hidden);
                }, 100);
            });

        } else {
            console.error('找不到输入框: ' + config.id);
        }
    });
}

// 处理剪贴板文本（专门用于粘贴事件）
function processClipboardText(text, containerId, hiddenId) {
    if (!text) return;

    console.log('处理剪贴板文本: "' + text + '"');
    console.log('文本长度: ' + text.length);

    // 显示每个字符的编码，帮助调试
    for (var i = 0; i < Math.min(text.length, 20); i++) {
        console.log('字符 ' + i + ': "' + text.charAt(i) + '" (编码: ' + text.charCodeAt(i) + ')');
    }

    // 检测换行符
    var hasNewline = false;
    var newlineChars = ['\n', '\r', '\r\n'];
    for (var i = 0; i < newlineChars.length; i++) {
        if (text.indexOf(newlineChars[i]) !== -1) {
            hasNewline = true;
            console.log('发现换行符: ' + newlineChars[i] + ' 在位置: ' + text.indexOf(newlineChars[i]));
            break;
        }
    }

    console.log('剪贴板文本是否包含换行符: ' + hasNewline);

    var items = [];
    if (hasNewline) {
        // 有换行符，按行分割
        items = text.split(/[\r\n]+/);
        // 过滤空行
        items = items.filter(function(item) {
            return item.trim().length > 0;
        });
        console.log('按换行符分割，得到 ' + items.length + ' 个项目:');
        items.forEach(function(item, index) {
            console.log('  项目 ' + (index + 1) + ': "' + item.trim() + '"');
        });
    } else {
        // 没有换行符，整个作为一个标签（保留空格）
        var trimmedText = text.trim();
        if (trimmedText) {
            items = [trimmedText];
            console.log('没有换行符，作为单个标签: "' + trimmedText + '"');
        }
    }

    // 创建标签
    items.forEach(function(item, index) {
        var trimmedItem = item.trim();
        if (trimmedItem) {
            console.log('创建第 ' + (index + 1) + ' 个标签: "' + trimmedItem + '"');
            createSimpleTag(trimmedItem, containerId, hiddenId);
        }
    });
}

// 处理输入
function processSimpleInput(input, containerId, hiddenId) {
    var value = input.value;
    if (!value) return;

    console.log('处理输入原始值: "' + value + '"');
    console.log('输入值长度: ' + value.length);

    // 显示每个字符的编码，帮助调试
    for (var i = 0; i < Math.min(value.length, 20); i++) {
        console.log('字符 ' + i + ': "' + value.charAt(i) + '" (编码: ' + value.charCodeAt(i) + ')');
    }

    // 更强力的换行符检测
    var hasNewline = false;
    var newlineChars = ['\n', '\r', '\r\n'];
    for (var i = 0; i < newlineChars.length; i++) {
        if (value.indexOf(newlineChars[i]) !== -1) {
            hasNewline = true;
            console.log('发现换行符: ' + newlineChars[i] + ' 在位置: ' + value.indexOf(newlineChars[i]));
            break;
        }
    }

    console.log('是否包含换行符: ' + hasNewline);

    var items = [];
    if (hasNewline) {
        // 有换行符，按行分割
        items = value.split(/[\r\n]+/);
        // 过滤空行
        items = items.filter(function(item) {
            return item.trim().length > 0;
        });
        console.log('按换行符分割，得到 ' + items.length + ' 个项目:');
        items.forEach(function(item, index) {
            console.log('  项目 ' + (index + 1) + ': "' + item.trim() + '"');
        });
    } else {
        // 没有换行符，整个作为一个标签（保留空格）
        var trimmedValue = value.trim();
        if (trimmedValue) {
            items = [trimmedValue];
            console.log('没有换行符，作为单个标签: "' + trimmedValue + '"');
        }
    }

    // 创建标签
    items.forEach(function(item, index) {
        var trimmedItem = item.trim();
        if (trimmedItem) {
            console.log('创建第 ' + (index + 1) + ' 个标签: "' + trimmedItem + '"');
            createSimpleTag(trimmedItem, containerId, hiddenId);
        }
    });

    // 清空输入框
    input.value = '';
}

// 创建标签
function createSimpleTag(text, containerId, hiddenId) {
    var container = document.getElementById(containerId);
    if (!container) return;

    // 创建标签元素
    var tag = document.createElement('span');
    tag.className = 'tag';
    tag.innerHTML = text + ' <span class="remove" onclick="removeSimpleTag2(this)">×</span>';

    container.appendChild(tag);
    console.log('创建标签: ' + text);
}

// 删除标签 - 设置为全局函数
window.removeSimpleTag2 = function(btn) {
    try {
        const tag = btn.parentElement;
        const container = tag.parentElement;

        // 移除标签
        tag.remove();

        // 更新对应的隐藏字段
        updateContainerHiddenField(container);

        // 更新底部生成预览中的状态
        setTimeout(updateBottomPreviewFromCurrentState, 100);

        console.log('删除标签成功');
    } catch (error) {
        console.error('删除标签失败:', error);
    }
};

// 更新容器对应的隐藏字段
function updateContainerHiddenField(container) {
    const containerId = container.id;
    let hiddenFieldId = '';

    // 根据容器ID确定对应的隐藏字段
    switch(containerId) {
        case 'required-topics-container':
            hiddenFieldId = 'required_topics';
            break;
        case 'random-topics-container':
            hiddenFieldId = 'random_topics';
            break;
        case 'at-users-container':
            hiddenFieldId = 'at_users';
            break;
        case 'locations-container':
            hiddenFieldId = 'location';
            break;
        default:
            console.log('未知的容器ID:', containerId);
            return;
    }

    // 收集容器中的所有标签文本
    const tags = container.querySelectorAll('.tag');
    const values = [];
    tags.forEach(tag => {
        const text = tag.textContent.replace('×', '').trim();
        if (text) {
            values.push(text);
        }
    });

    // 更新隐藏字段
    const hiddenField = document.getElementById(hiddenFieldId);
    if (hiddenField) {
        hiddenField.value = values.join('\n');
        console.log(`更新隐藏字段 ${hiddenFieldId}:`, values);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== 页面DOM加载完成，开始初始化 ===');

    // 立即尝试初始化
    if (window.initContentPageInputs) {
        window.initContentPageInputs();
    }

    // 延迟初始化（处理动态加载的情况）
    setTimeout(function() {
        console.log('=== 延迟初始化开始 ===');
        if (window.initContentPageInputs) {
            window.initContentPageInputs();
        }
    }, 1000);

    // 再次延迟初始化
    setTimeout(function() {
        console.log('=== 再次延迟初始化开始 ===');
        if (window.initContentPageInputs) {
            window.initContentPageInputs();
        }
    }, 3000);

    // 初始化客户选择（移除，避免重复触发）
    console.log('客户选择初始化逻辑已移动到延迟触发部分');

    // 初始化新任务名称失焦事件
    if (typeof initNewTaskNameBlur === 'function') {
        initNewTaskNameBlur();
    }

    // 初始化客户和任务选择
    initClientChange();
    initTaskChange();
    initTaskSelection();

    // 延迟初始化客户选择，确保页面完全加载
    setTimeout(() => {
        console.log('=== 延迟初始化客户选择开始 ===');

        // 检查页面中所有的select元素
        const allSelects = document.querySelectorAll('select');
        console.log('页面中所有select元素:', allSelects);
        allSelects.forEach((select, index) => {
            console.log(`Select ${index}:`, {
                id: select.id,
                name: select.name,
                className: select.className,
                optionsCount: select.options.length
            });
        });

        const clientSelect = document.getElementById('client_id');
        if (clientSelect && clientSelect.options.length > 0) {
            console.log('✅ 找到客户选择器，客户选项数量:', clientSelect.options.length);
            console.log('当前客户选择值:', clientSelect.value);

            // 如果没有选择客户，自动选择第一个有效的客户
            if (!clientSelect.value || clientSelect.value === '') {
                console.log('没有选择客户，自动选择第一个有效客户');
                // 找到第一个有效的客户选项（值不为空）
                for (let i = 0; i < clientSelect.options.length; i++) {
                    if (clientSelect.options[i].value && clientSelect.options[i].value !== '') {
                        clientSelect.selectedIndex = i;
                        console.log('自动选择客户:', clientSelect.options[i].text, '(ID:', clientSelect.options[i].value, ')');

                        // 触发客户选择变更事件，加载任务和默认值
                        const clientId = clientSelect.options[i].value;
                        const clientName = clientSelect.options[i].text;
                        loadTasksForClient(clientId, clientName);
                        loadClientDefaults(clientId);
                        break;
                    }
                }
            } else {
                // 如果已经选择了客户，也要加载默认值
                const clientId = clientSelect.value;
                if (clientId) {
                    console.log('页面初始化时加载已选择客户的默认值:', clientId);
                    loadClientDefaults(clientId);
                }
            }

            // 触发change事件加载任务
            if (clientSelect.value) {
                console.log('触发客户选择变更事件，客户ID:', clientSelect.value);
                const clientName = clientSelect.options[clientSelect.selectedIndex].text;
                console.log('客户名称:', clientName);
                // 直接调用加载任务的函数，而不是触发change事件
                loadTasksForClient(clientSelect.value, clientName);
            }
        } else {
            console.error('❌ 延迟初始化时仍然找不到客户选择器或没有选项');
        }
    }, 800);

    // 初始化模板分类选择变更事件
    initTemplateCategoryChange();

    // 初始化刷新按钮
    initRefreshButton();

    // 初始化检测功能
    initValidationFeatures();

    console.log('简化版本初始化完成');

    // 初始化表单验证
    if (typeof initFormValidation === 'function') {
        initFormValidation();
    }

    // 初始化数量验证
    if (typeof initCountValidation === 'function') {
        initCountValidation();
    }

    console.log('所有功能初始化完成');
});

// 简化的标签输入设置函数
function setupTagInput(inputId, containerId, hiddenFieldId) {
    try {
        console.log('>>> 开始设置标签输入: ' + inputId);

        const input = document.getElementById(inputId);
        const container = document.getElementById(containerId);

        console.log('输入框 ' + inputId + ': ' + (input ? '找到' : '未找到'));
        console.log('容器 ' + containerId + ': ' + (container ? '找到' : '未找到'));

        if (!input) {
            console.error('❌ 找不到输入框: ' + inputId);
            return;
        }
        if (!container) {
            console.error('❌ 找不到容器: ' + containerId);
            return;
        }

        console.log('✅ 元素都找到了，开始添加事件监听器...');

        // 按键事件
        input.addEventListener('keydown', function(e) {
            console.log('按键事件触发: ' + e.key);
            if (e.key === 'Enter') {
                console.log('检测到回车键，阻止表单提交并处理输入');
                e.preventDefault(); // 阻止表单提交
                processInput(this, containerId, hiddenFieldId);
            }
        });

        // 失去焦点事件
        input.addEventListener('blur', function() {
            console.log('失去焦点事件触发');
            processInput(this, containerId, hiddenFieldId);
        });

        console.log('✅ ' + inputId + ' 事件监听器设置完成');

    } catch (error) {
        console.error('设置 ' + inputId + ' 时出错:', error);
    }
}

// 处理输入内容
function processInput(input, containerId, hiddenFieldId) {
    const value = input.value.trim();
    if (!value) return;

    console.log('处理输入: "' + value + '"');

    // 分割输入内容
    let items = [];
    if (value.includes('\n')) {
        // 多行输入
        items = value.split('\n').map(item => item.trim()).filter(item => item);
    } else if (value.includes(' ')) {
        // 空格分隔
        items = value.split(' ').map(item => item.trim()).filter(item => item);
    } else {
        // 单个项目
        items = [value];
    }

    // 添加每个项目为标签
    items.forEach(item => {
        createTag(item, containerId, hiddenFieldId);
    });

    // 清空输入框
    input.value = '';
}

// 创建标签
function createTag(text, containerId, hiddenFieldId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // 检查是否已存在
    const existing = container.querySelector('[data-text="' + text + '"]');
    if (existing) return;

    // 创建标签元素
    const tag = document.createElement('span');
    tag.className = 'tag';
    tag.setAttribute('data-text', text);
    tag.innerHTML = text + ' <span class="remove" onclick="removeTag(this, \'' + containerId + '\', \'' + hiddenFieldId + '\')">×</span>';

    container.appendChild(tag);
    updateHiddenField(containerId, hiddenFieldId);

    console.log('创建标签: ' + text);

    // 更新底部生成预览中的状态
    setTimeout(updateBottomPreviewFromCurrentState, 100);
}

// 删除标签
function removeTag(removeBtn, containerId, hiddenFieldId) {
    const tag = removeBtn.parentElement;
    tag.remove();
    updateHiddenField(containerId, hiddenFieldId);

    // 更新底部生成预览中的状态
    setTimeout(updateBottomPreviewFromCurrentState, 100);
}

// 更新隐藏字段
function updateHiddenField(containerId, hiddenFieldId) {
    const container = document.getElementById(containerId);
    const hiddenField = document.getElementById(hiddenFieldId);

    if (!container || !hiddenField) return;

    const tags = container.querySelectorAll('.tag');
    const values = Array.from(tags).map(tag => tag.getAttribute('data-text')).filter(text => text);

    hiddenField.value = values.join('\n');
    console.log('更新隐藏字段 ' + hiddenFieldId + ': ' + values.join(', '));
}

// === 以下是标记替换功能的代码，保持不变 ===

// 标记替换功能的初始化单个标签输入 - 已禁用，避免与initSimpleInputs冲突
function initSingleTagInput_ForMarks_DISABLED(inputId, containerId, hiddenFieldId) {
    console.log(`初始化单个标签输入: ${inputId} -> ${containerId}`);

    const input = document.getElementById(inputId);
    const container = document.getElementById(containerId);
    const hiddenField = document.getElementById(hiddenFieldId);

    if (!input) {
        console.error(`找不到输入框: ${inputId}`);
        return;
    }
    if (!container) {
        console.error(`找不到容器: ${containerId}`);
        return;
    }

    console.log(`成功找到元素: ${inputId}, ${containerId}, ${hiddenFieldId || 'N/A'}`);

    // 添加输入事件监听
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ',') {
            e.preventDefault();
            const value = this.value.trim();
            if (value) {
                // 检查是否包含多个值（换行符或空格分隔）
                let lines = [];

                // 首先尝试按换行符分割
                if (value.includes('\n') || value.includes('\r')) {
                    lines = value.split(/\r?\n/).filter(line => line.trim());
                    console.log(`${inputId} 按键事件-按换行符分割: ${JSON.stringify(lines)}`);
                }
                // 如果没有换行符，检查是否有空格分隔的多个值
                else if (value.includes(' ') && value.split(' ').length > 1) {
                    lines = value.split(/\s+/).filter(item => item.trim());
                    console.log(`${inputId} 按键事件-按空格分割: ${JSON.stringify(lines)}`);
                }
                // 单个值
                else {
                    lines = [value];
                    console.log(`${inputId} 按键事件-单个值: ${JSON.stringify(lines)}`);
                }

                if (lines.length > 1) {
                    console.log(`${inputId} 按键事件-检测到多个值，开始批量添加`);
                    // 批量添加
                    lines.forEach((line, index) => {
                        const trimmed = line.trim();
                        if (trimmed) {
                            console.log(`${inputId} 按键事件-添加第${index + 1}个: "${trimmed}"`);
                            addSimpleTag(trimmed, containerId, hiddenFieldId);
                        }
                    });
                } else {
                    console.log(`${inputId} 按键事件-单个添加: "${value}"`);
                    // 单个添加
                    addSimpleTag(value, containerId, hiddenFieldId);
                }
                this.value = '';
            }
        }
    });

    // 添加失去焦点事件
    input.addEventListener('blur', function() {
        const value = this.value.trim();
        console.log(`${inputId} 失去焦点，当前值: "${value}"`);
        if (value) {
            // 检查是否包含换行符或空格分隔的多个值（批量粘贴）
            let lines = [];

            // 首先尝试按换行符分割
            if (value.includes('\n') || value.includes('\r')) {
                lines = value.split(/\r?\n/).filter(line => line.trim());
                console.log(`${inputId} 按换行符分割: ${JSON.stringify(lines)}`);
            }
            // 如果没有换行符，检查是否有空格分隔的多个值
            else if (value.includes(' ') && value.split(' ').length > 1) {
                lines = value.split(/\s+/).filter(item => item.trim());
                console.log(`${inputId} 按空格分割: ${JSON.stringify(lines)}`);
            }
            // 单个值
            else {
                lines = [value];
                console.log(`${inputId} 单个值: ${JSON.stringify(lines)}`);
            }

            if (lines.length > 1) {
                console.log(`${inputId} 检测到多个值，开始批量添加`);
                // 批量添加
                lines.forEach((line, index) => {
                    const trimmed = line.trim();
                    if (trimmed) {
                        console.log(`${inputId} 添加第${index + 1}个: "${trimmed}"`);
                        addSimpleTag(trimmed, containerId, hiddenFieldId);
                    }
                });
            } else {
                console.log(`${inputId} 单个添加: "${value}"`);
                // 单个添加
                addSimpleTag(value, containerId, hiddenFieldId);
            }
            this.value = '';
        }
    });

    // 添加粘贴事件处理 - 简化版本
    input.addEventListener('paste', function(e) {
        console.log(`${inputId} 检测到粘贴事件`);

        // 使用延时处理，让浏览器先完成默认粘贴
        setTimeout(() => {
            const value = this.value.trim();
            console.log(`${inputId} 粘贴后的值: "${value}"`);

            if (value) {
                // 检查是否包含多个值（换行符或空格分隔）
                let lines = [];

                // 尝试多种分割方式
                if (value.includes('\n') || value.includes('\r')) {
                    lines = value.split(/\r?\n/).filter(line => line.trim());
                    console.log(`${inputId} 按换行符分割: ${JSON.stringify(lines)}`);
                } else if (value.includes(' ') && value.split(' ').length > 1) {
                    lines = value.split(/\s+/).filter(item => item.trim());
                    console.log(`${inputId} 按空格分割: ${JSON.stringify(lines)}`);
                } else {
                    lines = [value];
                    console.log(`${inputId} 单个值: ${JSON.stringify(lines)}`);
                }

                if (lines.length > 1) {
                    console.log(`${inputId} 检测到多个值，开始批量添加`);
                    // 批量添加
                    lines.forEach((line, index) => {
                        const trimmed = line.trim();
                        if (trimmed) {
                            console.log(`${inputId} 添加第${index + 1}个: "${trimmed}"`);
                            addSimpleTag(trimmed, containerId, hiddenFieldId);
                        }
                    });
                    this.value = '';
                    console.log(`${inputId} 批量添加完成，清空输入框`);
                }
            }
        }, 50); // 增加延时确保浏览器完成粘贴
    });
}

// 添加简单标签
function addSimpleTag(value, containerId, hiddenFieldId) {
    console.log('addSimpleTag 被调用: value="' + value + '", containerId="' + containerId + '", hiddenFieldId="' + hiddenFieldId + '"');

    if (!value) {
        console.log('addSimpleTag: 值为空，返回');
        return;
    }

    try {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器: ' + containerId);
            return;
        }
        console.log('addSimpleTag: 找到容器 ' + containerId);

        // 检查是否已存在相同标签
        const existingTags = container.querySelectorAll('.tag');
        for (let i = 0; i < existingTags.length; i++) {
            if (existingTags[i].textContent.trim().replace('×', '') === value) {
                console.log('addSimpleTag: 标签 "' + value + '" 已存在，不添加');
                return; // 已存在相同标签，不添加
            }
        }

        // 创建新标签
        const tag = document.createElement('span');
        tag.className = 'tag';
        tag.innerHTML = value + ' <span class="remove" onclick="removeSimpleTag(this, \'' + containerId + '\', \'' + hiddenFieldId + '\')">×</span>';
        console.log('addSimpleTag: 创建新标签 "' + value + '"');

        // 添加到容器
        container.appendChild(tag);
        console.log('addSimpleTag: 标签已添加到容器');

        // 更新隐藏字段
        updateHiddenField(containerId, hiddenFieldId);
        console.log('addSimpleTag: 隐藏字段已更新');

        // 同时添加到标记替换区域（暂时注释掉以避免错误）
        // addToMarkReplacementArea(value, containerId);

    } catch (error) {
        console.error('添加标签出错: ' + error.message);
    }
}

// 移除简单标签 - 全局函数
window.removeSimpleTag = function(element, containerId, hiddenFieldId) {
    try {
        const tag = element.parentNode;
        const container = tag.parentNode;

        // 获取要移除的标记值
        const tagValue = tag.textContent.trim().replace('×', '');

        // 移除标签
        container.removeChild(tag);

        // 更新隐藏字段
        updateHiddenField(containerId, hiddenFieldId);

        // 同时从标记替换区域移除（暂时注释掉以避免错误）
        // removeFromMarkReplacementArea(tagValue, containerId);

    } catch (error) {
        console.error(`移除标签出错: ${error.message}`);
    }
};

// 更新隐藏字段
function updateHiddenField(containerId, hiddenFieldId) {
    console.log(`updateHiddenField 被调用: containerId="${containerId}", hiddenFieldId="${hiddenFieldId}"`);

    try {
        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container) {
            console.error(`updateHiddenField: 找不到容器 ${containerId}`);
            return;
        }
        if (!hiddenField) {
            console.error(`updateHiddenField: 找不到隐藏字段 ${hiddenFieldId}`);
            return;
        }

        const tags = container.querySelectorAll('.tag');
        const values = [];
        console.log(`updateHiddenField: 找到 ${tags.length} 个标签`);

        tags.forEach(tag => {
            const text = tag.textContent.trim().replace('×', '');
            if (text) {
                values.push(text);
                console.log(`updateHiddenField: 添加值 "${text}"`);
            }
        });

        hiddenField.value = values.join('\n');
        console.log(`updateHiddenField: 隐藏字段值设置为: "${hiddenField.value}"`);

    } catch (error) {
        console.error(`更新隐藏字段出错: ${error.message}`);
    }
}

// 添加到标记替换区域
function addToMarkReplacementArea(value, containerId) {
    try {
        // 根据容器ID确定标记类型
        let markType = '';
        switch (containerId) {
            case 'required-topics-container':
                markType = '话题';
                break;
            case 'random-topics-container':
                markType = '话题';
                break;
            case 'at-users-container':
                markType = '@用户';
                break;
            case 'locations-container':
                markType = '定位';
                break;
            default:
                console.log(`未知的容器ID: ${containerId}`);
                return;
        }

        // 调用现有的addMarkTag函数添加到标记替换区域
        if (typeof addMarkTag === 'function') {
            addMarkTag(value, markType);
            console.log(`已将 "${value}" 添加到标记替换区域，标记类型: ${markType}`);
        } else {
            console.error('addMarkTag函数不存在');
        }

    } catch (error) {
        console.error(`添加到标记替换区域出错: ${error.message}`);
    }
}

// 从标记替换区域移除
function removeFromMarkReplacementArea(value, containerId) {
    try {
        // 根据容器ID确定标记类型
        let markType = '';
        switch (containerId) {
            case 'required-topics-container':
                markType = '话题';
                break;
            case 'random-topics-container':
                markType = '话题';
                break;
            case 'at-users-container':
                markType = '@用户';
                break;
            case 'locations-container':
                markType = '定位';
                break;
            default:
                console.log(`未知的容器ID: ${containerId}`);
                return;
        }

        // 查找并移除对应的标记标签
        const markContainer = document.getElementById(`mark-${markType}-container`);
        if (markContainer) {
            const tags = markContainer.querySelectorAll('.tag');
            tags.forEach(tag => {
                const tagText = tag.textContent.trim().replace('×', '');
                if (tagText === value) {
                    markContainer.removeChild(tag);
                    console.log(`已从标记替换区域移除 "${value}"，标记类型: ${markType}`);
                }
            });
        }

    } catch (error) {
        console.error(`从标记替换区域移除出错: ${error.message}`);
    }
}

// 收集标记替换数据并更新隐藏字段
function updateKeywordsField() {
    try {
        const keywordsData = [];

        // 查找所有带有data-mark属性的标签容器
        const tagsContainers = document.querySelectorAll('.tags-container[data-mark]');

        tagsContainers.forEach(container => {
            const mark = container.getAttribute('data-mark');
            if (mark) {
                const tags = container.querySelectorAll('.tag');
                const keywords = [];

                tags.forEach(tag => {
                    const text = tag.textContent.replace('×', '').trim();
                    if (text) {
                        keywords.push(text);
                    }
                });

                if (keywords.length > 0) {
                    keywordsData.push(`${mark}: ${keywords.join(', ')}`);
                }
            }
        });

        // 更新隐藏字段
        const keywordsField = document.getElementById('keywords');
        if (keywordsField) {
            keywordsField.value = keywordsData.join('\n');
            console.log('更新关键词字段:', keywordsData);
        }

    } catch (error) {
        console.error('更新关键词字段失败:', error);
    }
}

// 全局的任务名称显示更新函数
function updateTaskNameDisplay() {
    const taskSelect = document.getElementById('task_id');
    const newTaskNameInput = document.getElementById('new_task_name');
    const newTaskNameGroup = newTaskNameInput?.closest('.form-group');
    const hintElement = document.getElementById('task-name-hint');

    if (!taskSelect || !newTaskNameInput || !newTaskNameGroup) {
        console.error('找不到必要的DOM元素');
        return;
    }

    const selectedValue = taskSelect.value;
    const selectedIndex = taskSelect.selectedIndex;
    const selectedOption = selectedIndex >= 0 ? taskSelect.options[selectedIndex] : null;

    console.log('更新任务名称显示:', {
        selectedValue: selectedValue,
        selectedIndex: selectedIndex,
        selectedText: selectedOption?.text,
        isCreateNew: selectedValue === '0'
    });

    if (selectedValue === '0') {
        // 选择"创建新任务"，显示可编辑的新任务名称输入框
        newTaskNameGroup.style.display = 'block';
        newTaskNameInput.readOnly = false;
        newTaskNameInput.style.backgroundColor = '';
        newTaskNameInput.style.cursor = 'text';

        // 更新提示文本
        if (hintElement) {
            hintElement.textContent = '请输入新任务的名称';
            hintElement.className = 'text-muted';
        }

        // 如果输入框为空或者是之前的任务名称，设置默认的新任务名称
        if (!newTaskNameInput.value || newTaskNameInput.dataset.isExistingTask === 'true') {
            const now = new Date();
            const defaultTaskName = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日任务`;
            newTaskNameInput.value = defaultTaskName;
            newTaskNameInput.dataset.isExistingTask = 'false';

            // 检查默认任务名称是否已存在，如果存在则查询批次数量
            checkTaskNameAndUpdateBatch(defaultTaskName);
        }

        console.log('设置为创建新任务模式');
    } else {
        // 选择已有任务，显示只读的任务名称
        newTaskNameGroup.style.display = 'block';
        newTaskNameInput.readOnly = true;
        newTaskNameInput.style.backgroundColor = '#e9ecef';
        newTaskNameInput.style.cursor = 'not-allowed';

        // 更新提示文本
        if (hintElement) {
            hintElement.textContent = '已选择现有任务，任务名称不可修改';
            hintElement.className = 'text-info';
        }

        // 显示选中任务的名称
        const taskName = selectedOption.text;
        newTaskNameInput.value = taskName;
        newTaskNameInput.dataset.isExistingTask = 'true';

        console.log('设置为已有任务模式:', taskName);

        // 查询该任务的批次数量并更新批次名称
        const taskId = selectedValue;
        fetch(`/simple/contents/get-batch-count/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const batchNameInput = document.getElementById('batch_name');
                    if (batchNameInput) {
                        batchNameInput.value = `批次 ${data.next_batch_number}`;
                        console.log(`设置批次名称为: 批次 ${data.next_batch_number} (当前已有${data.batch_count}个批次)`);
                    }
                } else {
                    console.error('获取批次数量失败:', data.error);
                }
            })
            .catch(error => {
                console.error('获取批次数量失败:', error);
            });
    }
}

// 初始化任务选择功能
function initTaskSelection() {
    const taskSelect = document.getElementById('task_id');
    const newTaskNameInput = document.getElementById('new_task_name');
    const newTaskNameGroup = newTaskNameInput?.closest('.form-group');

    if (taskSelect && newTaskNameGroup && newTaskNameInput) {
        console.log('初始化任务选择功能');

        // 初始状态检查
        updateTaskNameDisplay();

        // 监听任务选择变化
        taskSelect.addEventListener('change', function() {
            console.log('任务选择发生变化:', this.value);
            updateTaskNameDisplay();
        });

        // 监听任务名称输入框的失焦事件
        newTaskNameInput.addEventListener('blur', function() {
            const taskSelect = document.getElementById('task_id');
            if (taskSelect && taskSelect.value === '0') {
                // 只有在创建新任务模式下才检查
                const taskName = this.value.trim();
                if (taskName) {
                    console.log('任务名称失焦，检查批次数量:', taskName);
                    checkTaskNameAndUpdateBatch(taskName);
                }
            }
        });

        console.log('任务选择功能初始化完成');
    } else {
        console.error('任务选择功能初始化失败，找不到必要的DOM元素');
    }
}

// 初始化模板分类选择变更事件
function initTemplateCategoryChange() {
    console.log('=== 初始化模板分类选择变更事件 ===');
    const templateCategorySelect = document.getElementById('template_category_id');
    console.log('模板分类选择器元素:', templateCategorySelect);

    if (templateCategorySelect) {
        console.log('找到模板分类选择器，添加change事件监听器');
        templateCategorySelect.addEventListener('change', function() {
            console.log('=== 模板分类选择发生变化 ===');
            const categoryId = this.value;
            const categoryText = this.options[this.selectedIndex].text;
            console.log('选择的分类ID:', categoryId);
            console.log('选择的分类名称:', categoryText);

            if (categoryId) {
                // 先清空标记容器
                const marksContainer = document.getElementById('marks-container');
                marksContainer.innerHTML = '<p class="text-muted">正在加载标记...</p>';

                // 获取该分类下的所有标记
                fetch(`/simple/contents/get-marks/${categoryId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // 清空标记容器
                        marksContainer.innerHTML = '';

                        console.log('获取标记响应数据:', data);
                        console.log('标记列表:', data.marks);
                        console.log('模板数量:', data.template_count);

                        if (data.success) {
                            // 更新标记信息
                            const marksInfo = document.getElementById('marks-info');
                            if (marksInfo) {
                                marksInfo.textContent = data.marks && data.marks.length > 0
                                    ? `共 ${data.marks.length} 个标记`
                                    : '无标记';
                            }

                            // 更新模板数量
                            const templateCount = document.getElementById('template-count');
                            if (templateCount) {
                                templateCount.textContent = data.template_count > 0
                                    ? `共 ${data.template_count} 个模板`
                                    : '无可用模板';
                            }

                            if (data.marks && data.marks.length > 0) {
                                // 创建标记输入区域
                                data.marks.forEach(mark => {
                                    createMarkInput(mark, marksContainer);
                                });
                            } else {
                                marksContainer.innerHTML = '<p class="text-muted">该分类下的模板没有需要替换的标记</p>';
                            }
                        } else {
                            marksContainer.innerHTML = '<p class="text-danger">加载标记失败</p>';
                        }
                    })
                    .catch(error => {
                        console.error('获取标记失败:', error);
                        marksContainer.innerHTML = '<p class="text-danger">加载标记失败，请重试</p>';
                    });
            } else {
                // 清空标记容器
                const marksContainer = document.getElementById('marks-container');
                marksContainer.innerHTML = '<p class="text-muted">请先选择模板分类，系统将自动加载需要替换的标记</p>';

                // 清空标记信息
                const marksInfo = document.getElementById('marks-info');
                if (marksInfo) marksInfo.textContent = '';

                const templateCount = document.getElementById('template-count');
                if (templateCount) templateCount.textContent = '未选择模板分类';
            }
        });
        console.log('模板分类选择变更事件监听器已添加');
    } else {
        console.error('找不到模板分类选择器，无法添加事件监听器');
    }
    console.log('=== 模板分类选择变更事件初始化完成 ===');
}

// 处理标记替换设置的剪贴板文本
function processMarkClipboardText(text, mark) {
    if (!text) return;

    console.log(`${mark} 处理剪贴板文本: "${text}"`);
    console.log(`${mark} 文本长度: ${text.length}`);

    // 显示每个字符的编码，帮助调试
    for (var i = 0; i < Math.min(text.length, 20); i++) {
        console.log(`${mark} 字符 ${i}: "${text.charAt(i)}" (编码: ${text.charCodeAt(i)})`);
    }

    // 检测换行符
    var hasNewline = false;
    var newlineChars = ['\n', '\r', '\r\n'];
    for (var i = 0; i < newlineChars.length; i++) {
        if (text.indexOf(newlineChars[i]) !== -1) {
            hasNewline = true;
            console.log(`${mark} 发现换行符: ${newlineChars[i]} 在位置: ${text.indexOf(newlineChars[i])}`);
            break;
        }
    }

    console.log(`${mark} 剪贴板文本是否包含换行符: ${hasNewline}`);

    var items = [];
    if (hasNewline) {
        // 有换行符，按行分割
        items = text.split(/[\r\n]+/);
        // 过滤空行
        items = items.filter(function(item) {
            return item.trim().length > 0;
        });
        console.log(`${mark} 按换行符分割，得到 ${items.length} 个项目:`);
        items.forEach(function(item, index) {
            console.log(`${mark}   项目 ${index + 1}: "${item.trim()}"`);
        });
    } else {
        // 没有换行符，整个作为一个标签（保留空格）
        var trimmedText = text.trim();
        if (trimmedText) {
            items = [trimmedText];
            console.log(`${mark} 没有换行符，作为单个标签: "${trimmedText}"`);
        }
    }

    // 创建标签
    items.forEach(function(item, index) {
        var trimmedItem = item.trim();
        if (trimmedItem) {
            console.log(`${mark} 创建第 ${index + 1} 个标签: "${trimmedItem}"`);
            addMarkTag(trimmedItem, mark);
        }
    });
}

// 创建标记输入区域
function createMarkInput(mark, container) {
    if (!mark) {
        console.error('尝试创建无效的标记输入区域，标记名称为空');
        return;
    }

    console.log(`创建标记输入区域: ${mark}`);

    try {
        // 创建标记输入组 - 使用2列布局，在小屏幕上变为1列
        const markGroup = document.createElement('div');
        markGroup.className = 'col-lg-6 col-md-12 mb-3';

        // 创建输入区域
        const inputContainer = document.createElement('div');
        inputContainer.className = 'mark-input-container';

        // 创建标记标签
        const markLabel = document.createElement('span');
        markLabel.className = 'mark-label';
        markLabel.textContent = `{${mark}}`;

        // 创建输入组
        const inputGroup = document.createElement('div');
        inputGroup.className = 'input-group flex-grow-1';

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'form-control';
        input.placeholder = `输入后回车添加`;
        input.dataset.mark = mark;

        // 创建标签容器
        const tagsContainer = document.createElement('div');
        tagsContainer.className = 'tags-container mt-1';
        tagsContainer.dataset.mark = mark;

        // 添加输入事件监听
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                const value = this.value.trim();
                console.log(`${mark} 按键事件，当前值: "${value}"`);

                if (value) {
                    // 检查是否包含多个值（只按换行符分割，保留空格）
                    let lines = [];

                    // 只按换行符分割，空格保留在标签内
                    if (value.includes('\n') || value.includes('\r')) {
                        lines = value.split(/\r?\n/).filter(line => line.trim());
                        console.log(`${mark} 按换行符分割: ${JSON.stringify(lines)}`);
                    }
                    // 单个值（保留空格）
                    else {
                        lines = [value];
                        console.log(`${mark} 单个值（保留空格）: ${JSON.stringify(lines)}`);
                    }

                    if (lines.length > 1) {
                        console.log(`${mark} 检测到多个值，开始批量添加`);
                        // 批量添加
                        lines.forEach((line, index) => {
                            const trimmed = line.trim();
                            if (trimmed) {
                                console.log(`${mark} 添加第${index + 1}个: "${trimmed}"`);
                                addMarkTag(trimmed, mark);
                            }
                        });
                    } else {
                        console.log(`${mark} 单个添加: "${value}"`);
                        // 单个添加
                        addMarkTag(value, mark);
                    }
                    this.value = '';
                }
            }
        });

        // 添加失去焦点事件
        input.addEventListener('blur', function() {
            const value = this.value.trim();
            console.log(`${mark} 失去焦点，当前值: "${value}"`);

            if (value) {
                // 检查是否包含多个值（只按换行符分割，保留空格）
                let lines = [];

                // 只按换行符分割，空格保留在标签内
                if (value.includes('\n') || value.includes('\r')) {
                    lines = value.split(/\r?\n/).filter(line => line.trim());
                    console.log(`${mark} 按换行符分割: ${JSON.stringify(lines)}`);
                }
                // 单个值（保留空格）
                else {
                    lines = [value];
                    console.log(`${mark} 单个值（保留空格）: ${JSON.stringify(lines)}`);
                }

                if (lines.length > 1) {
                    console.log(`${mark} 检测到多个值，开始批量添加`);
                    // 批量添加
                    lines.forEach((line, index) => {
                        const trimmed = line.trim();
                        if (trimmed) {
                            console.log(`${mark} 添加第${index + 1}个: "${trimmed}"`);
                            addMarkTag(trimmed, mark);
                        }
                    });
                } else {
                    console.log(`${mark} 单个添加: "${value}"`);
                    // 单个添加
                    addMarkTag(value, mark);
                }
                this.value = '';
            }
        });

        // 添加粘贴事件处理
        input.addEventListener('paste', function(e) {
            console.log(`${mark} 检测到粘贴事件`);

            // 尝试直接从剪贴板获取数据
            if (e.clipboardData && e.clipboardData.getData) {
                const pastedText = e.clipboardData.getData('text/plain');
                console.log(`${mark} 从剪贴板获取的文本: "${pastedText}"`);

                if (pastedText) {
                    e.preventDefault(); // 阻止默认粘贴行为

                    // 直接处理剪贴板文本，不依赖输入框
                    processMarkClipboardText(pastedText, mark);
                    return;
                }
            }

            // 如果无法直接获取剪贴板数据，使用延迟处理
            setTimeout(() => {
                const value = this.value.trim();
                console.log(`${mark} 粘贴后的值: "${value}"`);

                if (value) {
                    // 检查是否包含多个值（换行符分隔）
                    const lines = value.split(/\r?\n/).filter(line => line.trim());

                    if (lines.length > 1) {
                        console.log(`${mark} 检测到多行，开始批量添加: ${JSON.stringify(lines)}`);
                        // 批量添加
                        lines.forEach((line, index) => {
                            const trimmed = line.trim();
                            if (trimmed) {
                                console.log(`${mark} 添加第${index + 1}个: "${trimmed}"`);
                                addMarkTag(trimmed, mark);
                            }
                        });
                        this.value = '';
                        console.log(`${mark} 批量添加完成，清空输入框`);
                    } else {
                        console.log(`${mark} 单行粘贴，等待用户按回车或失去焦点`);
                        // 单行粘贴，不立即处理，等待用户按回车或失去焦点
                    }
                }
            }, 50); // 增加延时确保浏览器完成粘贴
        });

        // 将所有元素组合起来
        inputGroup.appendChild(input);
        inputContainer.appendChild(markLabel);
        inputContainer.appendChild(inputGroup);

        markGroup.appendChild(inputContainer);
        markGroup.appendChild(tagsContainer);

        // 添加到容器 - 如果是第一个元素，创建一个行容器
        if (container.children.length === 0 || container.lastChild.tagName !== 'DIV' || !container.lastChild.classList.contains('row')) {
            const rowContainer = document.createElement('div');
            rowContainer.className = 'row';
            container.appendChild(rowContainer);
            rowContainer.appendChild(markGroup);
        } else {
            // 添加到现有的行容器
            container.lastChild.appendChild(markGroup);
        }

    } catch (error) {
        console.error(`创建标记 ${mark} 输入区域失败:`, error);
        container.innerHTML += `<p class="text-danger">创建标记 ${mark} 输入区域失败</p>`;
    }
}

// 添加标记标签
function addMarkTag(value, mark) {
    if (!value) return;

    try {
        // 获取标签容器
        const tagsContainer = document.querySelector(`.tags-container[data-mark="${mark}"]`);
        if (!tagsContainer) {
            console.error(`找不到标记 ${mark} 的标签容器`);
            return;
        }

        // 检查是否已存在相同标签
        const existingTags = tagsContainer.querySelectorAll('.tag');
        for (let i = 0; i < existingTags.length; i++) {
            if (existingTags[i].textContent.trim().replace('×', '') === value) {
                return; // 已存在相同标签，不添加
            }
        }

        // 创建新标签
        const tag = document.createElement('span');
        tag.className = 'tag';
        tag.innerHTML = `${value} <span class="remove" onclick="removeMarkTag(this, '${mark}')">×</span>`;

        // 添加到容器
        tagsContainer.appendChild(tag);

        // 清除上方可能的状态提示
        setTimeout(clearTopStatusMessages, 50);

        // 更新底部生成预览中的标记状态
        setTimeout(updateBottomPreviewFromCurrentState, 100);

        // 更新关键词字段
        setTimeout(updateKeywordsField, 50);

    } catch (error) {
        console.error(`添加标记标签出错: ${error.message}`);
    }
}

// 移除标记标签 - 全局函数
window.removeMarkTag = function(element, mark) {
    try {
        const tag = element.parentNode;
        const tagsContainer = tag.parentNode;

        // 移除标签
        tagsContainer.removeChild(tag);

        // 清除上方可能的状态提示
        setTimeout(clearTopStatusMessages, 50);

        // 更新底部生成预览中的标记状态
        setTimeout(updateBottomPreviewFromCurrentState, 100);

        // 更新关键词字段
        setTimeout(updateKeywordsField, 50);

    } catch (error) {
        console.error(`移除标记标签出错: ${error.message}`);
    }
};

// 检查任务名称是否已存在，并更新批次名称
function checkTaskNameAndUpdateBatch(taskName) {
    const clientSelect = document.getElementById('client_id');
    if (!clientSelect || !clientSelect.value) {
        console.log('没有选择客户，无法检查任务名称');
        return;
    }

    const clientId = clientSelect.value;
    console.log(`检查任务名称是否存在: ${taskName}, 客户ID: ${clientId}`);

    // 查询该客户下是否已有同名任务
    fetch(`/simple/contents/check-task-name`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
            client_id: clientId,
            task_name: taskName
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const batchNameInput = document.getElementById('batch_name');
            if (batchNameInput) {
                if (data.task_exists) {
                    // 任务已存在，设置下一个批次号
                    batchNameInput.value = `批次 ${data.next_batch_number}`;
                    console.log(`任务已存在，设置批次名称为: 批次 ${data.next_batch_number} (当前已有${data.batch_count}个批次)`);
                } else {
                    // 新任务，设置为批次1
                    batchNameInput.value = '批次 1';
                    console.log('新任务，设置批次名称为: 批次 1');
                }
            }
        } else {
            console.error('检查任务名称失败:', data.error);
        }
    })
    .catch(error => {
        console.error('检查任务名称失败:', error);
        // 如果检查失败，设置默认批次名称
        const batchNameInput = document.getElementById('batch_name');
        if (batchNameInput && !batchNameInput.value) {
            batchNameInput.value = '批次 1';
            console.log('检查失败，设置默认批次名称: 批次 1');
        }
    });
}

// 客户选择变更处理函数 - 设置为全局函数
window.onClientChange = function(selectElement) {
    const clientId = selectElement.value;
    const clientName = selectElement.options[selectElement.selectedIndex].text;

    console.log('客户选择变更:', clientName, '(ID:', clientId, ')');

    if (clientId) {
        loadTasksForClient(clientId, clientName);
        // 加载客户默认值
        loadClientDefaults(clientId);
    } else {
        // 客户未选择，清空任务列表
        const taskSelect = document.getElementById('task_id');
        if (taskSelect) {
            taskSelect.innerHTML = '<option value="0">-- 请先选择客户 --</option>';
            taskSelect.value = '0';
            updateTaskNameDisplay();

            // 重置批次名称
            const batchNameInput = document.getElementById('batch_name');
            if (batchNameInput) {
                batchNameInput.value = '批次 1';
                console.log('客户未选择，重置批次名称为: 批次 1');
            }
        }

        // 清空默认值
        clearClientDefaults();
    }
};

// 加载客户默认值
function loadClientDefaults(clientId) {
    console.log('🔄 加载客户默认值:', clientId);

    fetch(`/simple/api/clients/${clientId}/defaults`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ 客户默认值加载成功:', data.data);
                applyClientDefaults(data.data);
            } else {
                console.warn('⚠️ 加载客户默认值失败:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ 加载客户默认值出错:', error);
        });
}

// 应用客户默认值
function applyClientDefaults(defaults) {
    console.log('🎯 应用客户默认值:', defaults);

    // 应用必选话题
    if (defaults.required_topics && defaults.required_topics.length > 0) {
        const container = document.getElementById('required-topics-container');
        if (container) {
            // 清空现有标签
            container.innerHTML = '';

            // 添加默认话题
            defaults.required_topics.forEach(topic => {
                createTag(topic, 'required-topics-container', 'required_topics');
            });

            console.log('✅ 已应用默认必选话题:', defaults.required_topics);
        }
    }

    // 应用随机话题
    if (defaults.random_topics && defaults.random_topics.length > 0) {
        const container = document.getElementById('random-topics-container');
        if (container) {
            // 清空现有标签
            container.innerHTML = '';

            // 添加默认话题
            defaults.random_topics.forEach(topic => {
                createTag(topic, 'random-topics-container', 'random_topics');
            });

            console.log('✅ 已应用默认随机话题:', defaults.random_topics);
        }
    }

    // 应用@用户
    if (defaults.at_users && defaults.at_users.length > 0) {
        const container = document.getElementById('at-users-container');
        if (container) {
            // 清空现有标签
            container.innerHTML = '';

            // 添加默认@用户
            defaults.at_users.forEach(user => {
                createTag(user, 'at-users-container', 'at_users');
            });

            console.log('✅ 已应用默认@用户:', defaults.at_users);
        }
    }

    // 应用定位信息
    if (defaults.location) {
        const container = document.getElementById('locations-container');
        if (container) {
            // 清空现有标签
            container.innerHTML = '';

            // 添加默认定位
            createTag(defaults.location, 'locations-container', 'location');

            console.log('✅ 已应用默认定位信息:', defaults.location);
        }
    }

    // 显示提示
    if (typeof showToast === 'function') {
        const appliedItems = [];
        if (defaults.required_topics && defaults.required_topics.length > 0) appliedItems.push('必选话题');
        if (defaults.random_topics && defaults.random_topics.length > 0) appliedItems.push('随机话题');
        if (defaults.at_users && defaults.at_users.length > 0) appliedItems.push('@用户');
        if (defaults.location) appliedItems.push('定位信息');

        if (appliedItems.length > 0) {
            showToast(`已自动填充客户默认值：${appliedItems.join('、')}`, 'success');
        }
    }
}

// 清空客户默认值
function clearClientDefaults() {
    console.log('🧹 清空客户默认值');

    // 清空所有容器
    const containers = [
        'required-topics-container',
        'random-topics-container',
        'at-users-container',
        'locations-container'
    ];

    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '';
            updateContainerHiddenField(container);
        }
    });
}

// 加载指定客户的任务列表 - 设置为全局函数
window.loadTasksForClient = function(clientId, clientName) {
    console.log(`加载客户 ${clientName} 的任务列表...`);

    // 加载该客户的任务列表
    fetch(`/simple/contents/get-tasks/${clientId}`)
        .then(response => response.json())
        .then(data => {
            const taskSelect = document.getElementById('task_id');
            if (taskSelect && data.success) {
                console.log(`✅ 成功加载 ${data.tasks.length} 个任务`);

                // 清空现有选项
                taskSelect.innerHTML = '';

                // 添加"创建新任务"选项
                const createNewOption = document.createElement('option');
                createNewOption.value = '0';
                createNewOption.textContent = '-- 创建新任务 --';
                taskSelect.appendChild(createNewOption);

                // 添加现有任务选项
                data.tasks.forEach(task => {
                    const option = document.createElement('option');
                    option.value = task.id;
                    option.textContent = task.name;
                    taskSelect.appendChild(option);
                });

                // 默认选择"创建新任务"
                taskSelect.value = '0';

                // 直接调用更新函数
                updateTaskNameDisplay();

                // 获取设置的任务名称并检查批次
                const newTaskNameInput = document.getElementById('new_task_name');
                if (newTaskNameInput && newTaskNameInput.value) {
                    const taskName = newTaskNameInput.value.trim();
                    console.log('客户选择变更后，检查任务名称的批次:', taskName);
                    // 检查任务名称是否已存在，并更新批次名称
                    checkTaskNameAndUpdateBatch(taskName);
                } else {
                    // 如果没有任务名称，重置批次名称为"批次 1"
                    const batchNameInput = document.getElementById('batch_name');
                    if (batchNameInput) {
                        batchNameInput.value = '批次 1';
                        console.log('客户选择变更后，重置批次名称为: 批次 1');
                    }
                }

                // 触发任务选择变更事件，确保其他相关逻辑也被执行
                taskSelect.dispatchEvent(new Event('change'));

                // 更新模板可用情况
                if (typeof updateTemplateAvailability === 'function') {
                    updateTemplateAvailability();
                }
            } else {
                console.error('加载任务失败');
            }
        })
        .catch(error => {
            console.error('获取任务列表失败:', error);
        });
};



// 初始化客户选择变更事件
function initClientChange() {
    console.log('=== 开始初始化客户选择变更事件 ===');

    // 检查所有可能的客户选择器ID
    const possibleIds = ['client_id', 'client-id', 'clientId'];
    let clientSelect = null;

    for (const id of possibleIds) {
        const element = document.getElementById(id);
        if (element) {
            console.log(`找到客户选择器，ID: ${id}`, element);
            clientSelect = element;
            break;
        }
    }

    if (!clientSelect) {
        console.error('❌ 找不到客户选择器！尝试的ID:', possibleIds);
        // 尝试通过其他方式查找
        clientSelect = document.querySelector('select[name="client_id"]');
        if (clientSelect) {
            console.log('✅ 通过name属性找到客户选择器:', clientSelect);
        } else {
            console.error('❌ 完全找不到客户选择器');
            return;
        }
    }

    console.log('✅ 客户选择器信息:');
    console.log('  - 元素:', clientSelect);
    console.log('  - ID:', clientSelect.id);
    console.log('  - Name:', clientSelect.name);
    console.log('  - 选项数量:', clientSelect.options.length);
    console.log('  - 当前值:', clientSelect.value);
    console.log('  - 所有选项:', Array.from(clientSelect.options).map(opt => ({value: opt.value, text: opt.text})));

    if (clientSelect) {

        clientSelect.addEventListener('change', function() {
            const clientId = this.value;
            const clientName = this.options[this.selectedIndex].text;
            console.log('=== 客户选择变更事件触发 ===');
            console.log('客户ID:', clientId, '客户名称:', clientName);

            if (clientId) {
                // 加载该客户的任务列表
                fetch(`/simple/contents/get-tasks/${clientId}`)
                    .then(response => response.json())
                    .then(data => {
                        console.log('获取任务列表:', data);

                        const taskSelect = document.getElementById('task_id');
                        if (taskSelect && data.success) {
                            // 显示加载成功的提示
                            console.log(`✅ 成功加载客户 ${clientName} 的 ${data.tasks.length} 个任务`);

                            // 清空现有选项
                            taskSelect.innerHTML = '';

                            // 添加"创建新任务"选项
                            const createNewOption = document.createElement('option');
                            createNewOption.value = '0';
                            createNewOption.textContent = '-- 创建新任务 --';
                            taskSelect.appendChild(createNewOption);

                            // 添加现有任务选项
                            data.tasks.forEach(task => {
                                const option = document.createElement('option');
                                option.value = task.id;
                                option.textContent = task.name;
                                taskSelect.appendChild(option);
                                console.log(`  添加任务: ${task.name} (ID: ${task.id})`);
                            });

                            // 默认选择"创建新任务"
                            taskSelect.value = '0';

                            // 直接调用更新函数
                            updateTaskNameDisplay();

                            // 获取设置的任务名称并检查批次
                            const newTaskNameInput = document.getElementById('new_task_name');
                            if (newTaskNameInput && newTaskNameInput.value) {
                                const taskName = newTaskNameInput.value.trim();
                                console.log('客户选择变更后，检查任务名称的批次:', taskName);
                                // 检查任务名称是否已存在，并更新批次名称
                                checkTaskNameAndUpdateBatch(taskName);
                            }

                            // 更新模板可用情况
                            if (typeof updateTemplateAvailability === 'function') {
                                updateTemplateAvailability();
                            }

                            // 显示成功提示（可选）
                            if (data.tasks.length > 0) {
                                console.log(`📋 客户 ${clientName} 有 ${data.tasks.length} 个现有任务可选择`);
                            } else {
                                console.log(`📋 客户 ${clientName} 暂无现有任务，将创建新任务`);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('获取任务列表失败:', error);
                    });
            } else {
                // 客户未选择，清空任务列表
                const taskSelect = document.getElementById('task_id');
                if (taskSelect) {
                    taskSelect.innerHTML = '<option value="0">-- 请先选择客户 --</option>';
                    taskSelect.value = '0';
                    updateTaskNameDisplay();
                }
            }
        });
    }
}

// 初始化任务选择变更事件
function initTaskChange() {
    const taskSelect = document.getElementById('task_id');
    if (taskSelect) {
        taskSelect.addEventListener('change', function() {
            console.log('initTaskChange - 任务选择变更:', this.value, this.options[this.selectedIndex].text);
            // 直接调用更新函数
            updateTaskNameDisplay();
        });
    }
}

// 重复性控制相关功能
function initDuplicateControl() {
    // 监听重复性控制选项变化
    const duplicateRadios = document.querySelectorAll('input[name="duplicate_control"]');
    duplicateRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updateDuplicateMode();
            updateSelectedState();
            // 如果已经选择了模板分类，重新检测
            const categorySelect = document.getElementById('template_category_id');
            if (categorySelect && categorySelect.value) {
                validateGenerationCount();
            }
        });
    });

    // 监听生成数量输入框变化
    const countInput = document.getElementById('count');
    if (countInput) {
        countInput.addEventListener('input', function() {
            // 延迟检测，避免频繁请求
            clearTimeout(window.countValidationTimeout);
            window.countValidationTimeout = setTimeout(() => {
                const categorySelect = document.getElementById('template_category_id');
                if (categorySelect && categorySelect.value) {
                    validateGenerationCount();
                }
            }, 500);
        });
    }

    // 让整个区域可点击
    const customChecks = document.querySelectorAll('.form-check-custom');
    customChecks.forEach(checkDiv => {
        checkDiv.addEventListener('click', function(e) {
            // 如果点击的不是radio按钮本身，则触发radio按钮
            if (e.target.type !== 'radio') {
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                    radio.dispatchEvent(new Event('change'));
                }
            }
        });
    });

    // 初始化显示
    updateDuplicateMode();
    updateSelectedState();
}

function updateSelectedState() {
    // 更新选中状态的视觉效果（兼容不支持:has()的浏览器）
    const customChecks = document.querySelectorAll('.form-check-custom');
    customChecks.forEach(checkDiv => {
        const radio = checkDiv.querySelector('input[type="radio"]');
        if (radio && radio.checked) {
            checkDiv.classList.add('selected');
        } else {
            checkDiv.classList.remove('selected');
        }
    });
}

function updateDuplicateMode() {
    const selectedRadio = document.querySelector('input[name="duplicate_control"]:checked');
    const modeText = {
        'client': '客户不重复',
        'task': '任务不重复',
        'batch': '批次不重复'
    };

    const duplicateModeSpan = document.getElementById('duplicate-mode');
    if (duplicateModeSpan && selectedRadio) {
        duplicateModeSpan.textContent = modeText[selectedRadio.value] || '未知';
    }
}

// 生成数量检测功能
function validateGenerationCount() {
    console.log('🔄 validateGenerationCount 函数被调用');

    const countInput = document.getElementById('count');
    const categorySelect = document.getElementById('template_category_id');
    const clientSelect = document.getElementById('client_id');
    const taskSelect = document.getElementById('task_id');
    const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked');

    console.log('🔍 表单元素检查:', {
        countInput: countInput,
        countValue: countInput ? countInput.value : 'null',
        categorySelect: categorySelect,
        categoryValue: categorySelect ? categorySelect.value : 'null',
        clientSelect: clientSelect,
        taskSelect: taskSelect,
        duplicateControl: duplicateControl
    });

    if (!categorySelect.value) {
        updatePreviewInfo({
            error: '请先选择模板分类'
        });
        return;
    }

    // 获取任务名称
    let taskName = null;
    if (taskSelect.value === '0') {
        // 创建新任务，获取任务名称输入框的值
        const newTaskNameInput = document.getElementById('new_task_name');
        taskName = newTaskNameInput ? newTaskNameInput.value.trim() : null;
    }

    const requestData = {
        template_category_id: categorySelect.value,
        client_id: clientSelect.value,
        task_id: taskSelect.value,
        task_name: taskName,
        duplicate_control: duplicateControl ? duplicateControl.value : 'task',
        requested_count: parseInt(countInput.value) || 0
    };

    // 显示加载状态
    updatePreviewInfo({
        loading: true
    });

    // 获取CSRF令牌
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    fetch('/simple/validate-generation-count', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('检测结果:', data);
        updatePreviewInfo(data);
    })
    .catch(error => {
        console.error('检测失败:', error);
        updatePreviewInfo({
            error: '检测失败，请稍后重试'
        });
    });
}

function updatePreviewInfo(data) {
    const availableCountSpan = document.getElementById('available-count');
    const selectedCategorySpan = document.getElementById('selected-category');
    const totalTemplatesSpan = document.getElementById('total-templates');
    const usableTemplatesSpan = document.getElementById('usable-templates');
    const validationWarnings = document.getElementById('validation-warnings');
    const countInput = document.getElementById('count');

    if (data.loading) {
        availableCountSpan.textContent = '检测中...';
        availableCountSpan.className = 'preview-value';
        validationWarnings.innerHTML = '';
        return;
    }

    if (data.error) {
        availableCountSpan.textContent = '错误';
        availableCountSpan.className = 'preview-value text-danger';
        selectedCategorySpan.textContent = '未选择';
        totalTemplatesSpan.textContent = '0';
        usableTemplatesSpan.textContent = '0';
        validationWarnings.innerHTML = `<div class="alert alert-danger alert-sm">${data.error}</div>`;
        return;
    }

    // 更新基本信息
    if (data.success) {
        selectedCategorySpan.textContent = data.category_name || '未知';
        totalTemplatesSpan.textContent = data.total_templates || 0;
        usableTemplatesSpan.textContent = data.usable_templates || 0;

        // 更新底部生成预览中的标记状态
        updateBottomPreviewMarkStatus(data);

        // 更新可生成数量
        const availableCount = data.available_count || 0;
        availableCountSpan.textContent = availableCount;

        const requestedCount = parseInt(countInput.value) || 0;

        // 根据可用数量设置样式
        if (requestedCount > availableCount) {
            availableCountSpan.className = 'preview-value text-danger fw-bold';
        } else {
            availableCountSpan.className = 'preview-value text-success fw-bold';
        }

        // 更新警告信息
        let warnings = [];

        const totalTemplates = data.total_templates || 0;
        const usableTemplates = data.usable_templates || 0;
        const usedTemplates = totalTemplates - usableTemplates;

        if (requestedCount > availableCount) {
            const shortage = requestedCount - availableCount;
            warnings.push(`<div class="alert alert-danger alert-sm">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <strong>无法生成：</strong>请求生成 <strong>${requestedCount}</strong> 篇，但最多只能生成 <strong>${availableCount}</strong> 篇
                <br><small class="text-muted">
                    该分类共有 ${totalTemplates} 个模板，已使用 ${usedTemplates} 个，剩余 ${usableTemplates} 个可用
                    <br>缺少 <span class="text-danger fw-bold">${shortage}</span> 个可用模板
                </small>
            </div>`);
        } else if (requestedCount === availableCount && availableCount < totalTemplates) {
            warnings.push(`<div class="alert alert-warning alert-sm">
                <i class="bi bi-info-circle"></i>
                <strong>刚好用完：</strong>将生成 <strong>${requestedCount}</strong> 篇文案，用完所有可用模板
                <br><small class="text-muted">
                    该分类共有 ${totalTemplates} 个模板，已使用 ${usedTemplates} 个，剩余 ${usableTemplates} 个可用
                </small>
            </div>`);
        } else {
            warnings.push(`<div class="alert alert-success alert-sm">
                <i class="bi bi-check-circle-fill"></i>
                <strong>可以生成：</strong>将生成 <strong>${requestedCount}</strong> 篇文案
                <br><small class="text-muted">
                    该分类共有 ${totalTemplates} 个模板，已使用 ${usedTemplates} 个，剩余 ${usableTemplates} 个可用
                    ${availableCount > requestedCount ? `，生成后还剩 ${availableCount - requestedCount} 个可用` : ''}
                </small>
            </div>`);
        }

        validationWarnings.innerHTML = warnings.join('');
    }
}

// 立即尝试初始化（如果页面已经加载完成）
console.log('=== 脚本加载完成，立即尝试初始化 ===');
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log('页面已加载完成，立即初始化');
    if (window.initContentPageInputs) {
        window.initContentPageInputs();
    }

    // 初始化新功能
    initDuplicateControl();

    // 初始化检测功能
    initValidationFeatures();

    // 绑定刷新标记按钮事件 - 使用更强健的方法
    initRefreshButton();

    // 清除上方的状态提示
    setTimeout(clearTopStatusMessages, 100);
}

// 更新底部生成预览中的标记状态
function updateBottomPreviewMarkStatus(data) {
    const generatePreview = document.getElementById('generatePreview');
    if (!generatePreview) return;

    // 检查标记填写情况
    const userMarks = data.user_marks || [];
    let filledCount = 0;
    let unfilledMarks = [];

    userMarks.forEach(mark => {
        // 直接查找标签容器，使用data-mark属性
        const tagsContainer = document.querySelector(`.tags-container[data-mark="${mark}"]`);
        console.log(`检查标记: ${mark}, 标签容器:`, tagsContainer);

        if (tagsContainer) {
            const tags = tagsContainer.querySelectorAll('.tag');
            console.log(`标记 ${mark} 的标签数量:`, tags.length);

            if (tags.length > 0) {
                filledCount++;
                console.log(`标记 ${mark} 已填写`);
            } else {
                unfilledMarks.push(mark);
                console.log(`标记 ${mark} 未填写`);
            }
        } else {
            unfilledMarks.push(mark);
            console.log(`标记 ${mark} 找不到标签容器`);
        }
    });

    // 检查其他必填字段
    const otherFields = checkOtherRequiredFields();

    // 合并所有未填写的内容
    const allUnfilled = [...unfilledMarks, ...otherFields.unfilled];
    const allFilledCount = filledCount + otherFields.filledCount;
    const totalCount = userMarks.length + otherFields.totalCount;

    // 构建标记状态HTML
    let markStatusHtml = '';

    if (userMarks.length === 0 && otherFields.totalCount === 0) {
        markStatusHtml = `
            <div class="alert alert-info alert-sm mb-3">
                <i class="bi bi-info-circle"></i>
                <strong>内容状态：</strong>该分类下的模板无需填写标记，其他内容也无需填写
            </div>
        `;
    } else if (allUnfilled.length === 0) {
        markStatusHtml = `
            <div class="alert alert-success alert-sm mb-3">
                <i class="bi bi-check-circle"></i>
                <strong>内容状态：</strong>所有内容已填写完成 (${allFilledCount}/${totalCount})
            </div>
        `;
    } else {
        markStatusHtml = `
            <div class="alert alert-warning alert-sm mb-3">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>还有 ${allUnfilled.length} 项内容未填写：</strong>
                <br><span class="text-danger fw-bold">${allUnfilled.join('、')}</span>
                <br><small>请在上方相应区域中填写这些内容</small>
                <br><small class="text-muted">已填写：${allFilledCount}/${totalCount}</small>
            </div>
        `;
    }

    // 直接替换generatePreview的内容，确保不重复
    generatePreview.innerHTML = markStatusHtml;
}

// 检查其他必填字段（必选话题、随机话题、@用户、定位）
function checkOtherRequiredFields() {
    const unfilled = [];
    let filledCount = 0;
    let totalCount = 0;

    // 检查必选话题
    const requiredTopicsContainer = document.getElementById('required-topics-container');
    if (requiredTopicsContainer) {
        totalCount++;
        const topics = requiredTopicsContainer.querySelectorAll('.tag');
        if (topics.length > 0) {
            filledCount++;
        } else {
            unfilled.push('必选话题');
        }
    }

    // 检查随机话题
    const randomTopicsContainer = document.getElementById('random-topics-container');
    if (randomTopicsContainer) {
        totalCount++;
        const topics = randomTopicsContainer.querySelectorAll('.tag');
        if (topics.length > 0) {
            filledCount++;
        } else {
            unfilled.push('随机话题');
        }
    }

    // 检查@用户
    const atUsersContainer = document.getElementById('at-users-container');
    if (atUsersContainer) {
        totalCount++;
        const users = atUsersContainer.querySelectorAll('.tag');
        if (users.length > 0) {
            filledCount++;
        } else {
            unfilled.push('@用户');
        }
    }

    // 检查定位
    const locationsContainer = document.getElementById('locations-container');
    if (locationsContainer) {
        totalCount++;
        const locations = locationsContainer.querySelectorAll('.tag');
        if (locations.length > 0) {
            filledCount++;
        } else {
            unfilled.push('定位');
        }
    }

    return {
        unfilled: unfilled,
        filledCount: filledCount,
        totalCount: totalCount
    };
}

// 从当前状态更新底部生成预览中的标记状态
function updateBottomPreviewFromCurrentState() {
    // 获取当前选择的模板分类
    const categorySelect = document.getElementById('template_category_id');
    if (!categorySelect || !categorySelect.value) return;

    // 从当前DOM状态获取标记信息
    const tagsContainers = document.querySelectorAll('.tags-container[data-mark]');
    const userMarks = [];

    tagsContainers.forEach(container => {
        const markName = container.getAttribute('data-mark');
        if (markName) {
            userMarks.push(markName);
        }
    });

    console.log('从DOM获取的标记列表:', userMarks);

    const mockData = {
        user_marks: userMarks
    };

    updateBottomPreviewMarkStatus(mockData);
}

// 刷新当前分类的标记
function refreshCurrentCategoryMarks() {
    console.log('🔄 refreshCurrentCategoryMarks 函数被调用');
    const categorySelect = document.getElementById('template_category_id');
    console.log('🔍 模板分类选择器:', categorySelect);
    console.log('🔍 选择器的值:', categorySelect ? categorySelect.value : 'null');

    if (!categorySelect || !categorySelect.value) {
        console.log('❌ 没有选择模板分类');
        alert('请先选择模板分类');
        return;
    }

    const categoryId = categorySelect.value;
    const categoryText = categorySelect.options[categorySelect.selectedIndex].text;

    console.log('刷新标记，分类ID:', categoryId, '分类名称:', categoryText);

    // 显示刷新状态
    const refreshBtn = document.getElementById('refreshMarksBtn');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
    refreshBtn.disabled = true;

    // 先清空标记容器
    const marksContainer = document.getElementById('marks-container');
    if (marksContainer) {
        marksContainer.innerHTML = '<p class="text-muted">正在刷新标记...</p>';
    }

    // 获取该分类下的所有标记
    fetch(`/simple/contents/get-marks/${categoryId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('刷新标记响应数据:', data);

            // 再次清空标记容器，确保不会有残留
            marksContainer.innerHTML = '';

            if (data.success) {
                // 更新标记信息
                const marksInfo = document.getElementById('marks-info');
                if (marksInfo) {
                    marksInfo.textContent = data.marks && data.marks.length > 0
                        ? `共 ${data.marks.length} 个标记`
                        : '无标记';
                }

                // 更新模板数量
                const templateCount = document.getElementById('template-count');
                if (templateCount) {
                    templateCount.textContent = data.template_count > 0
                        ? `共 ${data.template_count} 个模板`
                        : '无可用模板';
                }

                if (data.marks && data.marks.length > 0) {
                    // 创建标记输入区域
                    data.marks.forEach(mark => {
                        createMarkInput(mark, marksContainer);
                    });
                    console.log('标记刷新完成，共创建', data.marks.length, '个标记输入区域');
                } else {
                    marksContainer.innerHTML = '<p class="text-muted">该分类下的模板没有需要替换的标记</p>';
                }

                // 更新底部生成预览
                setTimeout(updateBottomPreviewFromCurrentState, 100);

            } else {
                marksContainer.innerHTML = '<p class="text-danger">刷新标记失败</p>';
                console.error('刷新标记失败:', data.message);
            }
        })
        .catch(error => {
            console.error('刷新标记失败:', error);
            marksContainer.innerHTML = '<p class="text-danger">刷新标记失败，请重试</p>';
        })
        .finally(() => {
            // 恢复按钮状态
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        });
}

// 清除上方可能的状态提示
function clearTopStatusMessages() {
    // 查找所有alert元素，移除包含标记状态的提示
    const allAlerts = document.querySelectorAll('.alert');
    allAlerts.forEach(alert => {
        if (alert.textContent.includes('标记未填写') || alert.textContent.includes('标记替换设置')) {
            // 检查是否在生成预览区域内
            const isInPreview = alert.closest('#generatePreview') || alert.closest('.custom-info-panel');
            if (!isInPreview) {
                console.log('移除上方的标记状态提示:', alert.textContent);
                alert.remove();
            }
        }
    });
}

// 表单提交前的数据准备
window.prepareFormSubmission = function() {
    console.log('=== 准备表单提交，更新隐藏字段 ===');

    try {
        // 更新必选话题
        updateHiddenField('required-topics-container', 'required_topics');

        // 更新随机话题
        updateHiddenField('random-topics-container', 'random_topics');

        // 更新@用户
        updateHiddenField('at-users-container', 'at_users');

        // 更新位置信息
        updateHiddenField('locations-container', 'location');

        // 设置表单已验证标志
        const formValidated = document.getElementById('form_validated');
        if (formValidated) {
            formValidated.value = '1';
        }

        // 打印最终的隐藏字段值
        console.log('=== 最终隐藏字段值 ===');
        console.log('required_topics:', document.getElementById('required_topics')?.value);
        console.log('random_topics:', document.getElementById('random_topics')?.value);
        console.log('at_users:', document.getElementById('at_users')?.value);
        console.log('location:', document.getElementById('location')?.value);
        console.log('=== 隐藏字段值结束 ===');

        // 阻止默认表单提交，使用AJAX提交
        console.log('=== 开始AJAX提交表单 ===');
        
        // 获取表单数据
        const form = document.getElementById('generateForm');
        const formData = new FormData(form);
        
        // 显示提交状态
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
        
        // 发送AJAX请求
        fetch('/simple/content', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('收到响应，状态码:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('响应数据:', data);
            
            if (data.success) {
                alert(`✅ 生成成功！\n\n共生成了 ${data.generated_count} 篇文案\n\n可以到初审文案页面查看生成的内容`);
                
                // 清空表单
                if (data.clear_form) {
                    form.reset();
                    // 重新初始化页面
                    location.reload();
                }
            } else {
                alert(`❌ 生成失败！\n\n错误信息：${data.message || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('提交过程中出错:', error);
            alert(`❌ 提交过程中出错！\n\n错误信息：${error.message}`);
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });

        return false; // 阻止默认表单提交

    } catch (error) {
        console.error('准备表单提交时出错:', error);
        alert('准备提交数据时出错，请重试');
        return false; // 阻止表单提交
    }
}

// 强健的刷新按钮初始化函数
function initRefreshButton() {
    console.log('🔄 开始初始化刷新按钮');

    // 尝试多次查找和绑定刷新按钮
    let attempts = 0;
    const maxAttempts = 10;

    function tryBindRefreshButton() {
        attempts++;
        console.log(`🔍 第${attempts}次尝试查找刷新按钮`);

        const refreshMarksBtn = document.getElementById('refreshMarksBtn');
        console.log('🔍 查找刷新按钮结果:', refreshMarksBtn);

        if (refreshMarksBtn) {
            // 检查是否已经绑定过事件
            if (!refreshMarksBtn.dataset.eventBound) {
                console.log('✅ 找到刷新按钮，绑定点击事件');
                refreshMarksBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🔄 刷新按钮被点击');
                    refreshCurrentCategoryMarks();
                });

                // 标记已绑定事件，避免重复绑定
                refreshMarksBtn.dataset.eventBound = 'true';
                console.log('✅ 刷新按钮事件绑定完成');
                return true;
            } else {
                console.log('⚠️ 刷新按钮已经绑定过事件');
                return true;
            }
        } else {
            console.log(`❌ 第${attempts}次未找到刷新按钮`);

            if (attempts < maxAttempts) {
                // 继续尝试
                setTimeout(tryBindRefreshButton, 500);
            } else {
                console.error('❌ 达到最大尝试次数，仍未找到刷新按钮');

                // 使用事件委托作为备选方案
                console.log('🔄 使用事件委托作为备选方案');
                document.addEventListener('click', function(e) {
                    if (e.target && e.target.id === 'refreshMarksBtn') {
                        console.log('🔄 通过事件委托检测到刷新按钮点击');
                        e.preventDefault();
                        refreshCurrentCategoryMarks();
                    }
                });
            }
            return false;
        }
    }

    // 立即尝试
    if (!tryBindRefreshButton()) {
        // 如果立即尝试失败，延迟尝试
        setTimeout(tryBindRefreshButton, 100);
    }
}

// 强健的检测功能初始化函数
function initValidationFeatures() {
    console.log('🔄 开始初始化检测功能');

    // 尝试多次查找和绑定检测按钮
    let attempts = 0;
    const maxAttempts = 10;

    function tryInitValidation() {
        attempts++;
        console.log(`🔍 第${attempts}次尝试初始化检测功能`);

        // 查找检测按钮
        const validateBtn = document.getElementById('validateCountBtn');
        console.log('🔍 查找检测按钮结果:', validateBtn);

        if (validateBtn) {
            // 检查是否已经绑定过事件
            if (!validateBtn.dataset.validationBound) {
                console.log('✅ 找到检测按钮，绑定点击事件');
                validateBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🔄 检测按钮被点击');
                    validateGenerationCount();
                });

                // 标记已绑定事件
                validateBtn.dataset.validationBound = 'true';
                console.log('✅ 检测按钮事件绑定完成');
            } else {
                console.log('⚠️ 检测按钮已经绑定过事件');
            }

            // 绑定生成数量输入框的自动检测功能
            const countInput = document.getElementById('count');
            console.log('🔍 查找生成数量输入框:', countInput);
            if (countInput && !countInput.dataset.validationBound) {
                console.log('✅ 找到生成数量输入框，绑定输入事件');

                // 防抖处理，避免频繁触发
                let countInputTimeout;

                countInput.addEventListener('input', function() {
                    console.log('🔄 生成数量输入变化:', this.value);

                    // 清除之前的定时器
                    if (countInputTimeout) {
                        clearTimeout(countInputTimeout);
                    }

                    // 延迟500ms后执行检测
                    countInputTimeout = setTimeout(function() {
                        console.log('🔄 自动触发生成数量检测');
                        validateGenerationCount();
                    }, 500);
                });

                // 也监听失去焦点事件
                countInput.addEventListener('blur', function() {
                    console.log('🔄 生成数量输入框失去焦点，触发检测');
                    validateGenerationCount();
                });

                countInput.dataset.validationBound = 'true';
            }

            // 绑定其他相关表单元素的自动检测功能
            const elementsToWatch = [
                'template_category_id',
                'client_id',
                'task_id'
            ];

            elementsToWatch.forEach(function(elementId) {
                const element = document.getElementById(elementId);
                if (element && !element.dataset.validationBound) {
                    console.log('✅ 为元素绑定自动检测:', elementId);
                    element.addEventListener('change', function() {
                        console.log('🔄 表单元素变化，触发检测:', elementId, this.value);
                        // 延迟一点执行，确保其他相关逻辑先完成
                        setTimeout(function() {
                            validateGenerationCount();
                        }, 100);
                    });
                    element.dataset.validationBound = 'true';
                }
            });

            // 绑定重复控制选项的自动检测
            const duplicateRadios = document.querySelectorAll('input[name="duplicate_control"]');
            duplicateRadios.forEach(function(radio) {
                if (!radio.dataset.validationBound) {
                    radio.addEventListener('change', function() {
                        console.log('🔄 重复控制选项变化，触发检测:', this.value);
                        setTimeout(function() {
                            validateGenerationCount();
                        }, 100);
                    });
                    radio.dataset.validationBound = 'true';
                }
            });

            console.log('✅ 检测功能初始化完成');
            return true;
        } else {
            console.log(`❌ 第${attempts}次未找到检测按钮`);

            if (attempts < maxAttempts) {
                // 继续尝试
                setTimeout(tryInitValidation, 500);
            } else {
                console.error('❌ 达到最大尝试次数，仍未找到检测按钮');

                // 使用事件委托作为备选方案
                console.log('🔄 使用事件委托作为备选方案');
                document.addEventListener('click', function(e) {
                    if (e.target && e.target.id === 'validateCountBtn') {
                        console.log('🔄 通过事件委托检测到检测按钮点击');
                        e.preventDefault();
                        validateGenerationCount();
                    }
                });
            }
            return false;
        }
    }

    // 立即尝试
    if (!tryInitValidation()) {
        // 如果立即尝试失败，延迟尝试
        setTimeout(tryInitValidation, 100);
    }
}

console.log('文案生成页面脚本已加载完成');
</script>
