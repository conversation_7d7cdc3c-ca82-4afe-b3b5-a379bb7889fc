<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户默认值功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.info { background: #e3f2fd; color: #1976d2; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        .status.warning { background: #fff3e0; color: #f57c00; }
        .status.error { background: #ffebee; color: #c62828; }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 客户默认值功能测试</h1>
    
    <div class="card">
        <h3>功能说明</h3>
        <p>这个页面用于测试客户默认值功能：</p>
        <ul>
            <li>✅ 数据库字段已添加</li>
            <li>✅ 客户表单已更新</li>
            <li>✅ API接口已创建</li>
            <li>✅ 前端逻辑已实现</li>
        </ul>
    </div>
    
    <div class="card">
        <h3>测试步骤</h3>
        <ol>
            <li><strong>添加客户默认值</strong>：
                <ul>
                    <li>访问：<a href="http://127.0.0.1:5000/simple/clients" target="_blank">客户管理页面</a></li>
                    <li>点击"添加客户"或编辑现有客户</li>
                    <li>在表单底部填写默认值设置</li>
                    <li>保存客户信息</li>
                </ul>
            </li>
            <li><strong>测试默认值加载</strong>：
                <ul>
                    <li>访问：<a href="http://127.0.0.1:5000/simple/content" target="_blank">内容生成页面</a></li>
                    <li>选择设置了默认值的客户</li>
                    <li>观察是否自动填充默认话题、@用户、定位信息</li>
                </ul>
            </li>
            <li><strong>API测试</strong>：
                <ul>
                    <li>使用下面的测试工具验证API接口</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="card">
        <h3>API接口测试</h3>
        <div class="form-group">
            <label>客户ID：</label>
            <input type="number" id="clientId" placeholder="输入客户ID" value="1">
        </div>
        <button class="btn" onclick="testClientDefaultsAPI()">测试获取客户默认值API</button>
        <div id="apiResult" class="test-result" style="display: none;"></div>
    </div>
    
    <div class="card">
        <h3>数据库验证</h3>
        <p>检查数据库中是否正确添加了字段：</p>
        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
-- 查看客户表结构
DESCRIBE clients;

-- 查看客户默认值数据
SELECT id, name, default_required_topics, default_random_topics, default_at_users, default_location 
FROM clients 
WHERE default_required_topics IS NOT NULL 
   OR default_random_topics IS NOT NULL 
   OR default_at_users IS NOT NULL 
   OR default_location IS NOT NULL;
        </pre>
    </div>
    
    <div class="card">
        <h3>测试日志</h3>
        <div id="testLog" style="background: #f8f9fa; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // 测试客户默认值API
        function testClientDefaultsAPI() {
            const clientId = document.getElementById('clientId').value;
            if (!clientId) {
                alert('请输入客户ID');
                return;
            }
            
            log(`开始测试客户 ${clientId} 的默认值API...`);
            
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在请求...';
            
            fetch(`/simple/api/clients/${clientId}/defaults`)
                .then(response => {
                    log(`API响应状态: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    log(`API响应成功: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success) {
                        const defaults = data.data;
                        let result = '✅ API测试成功！\n\n';
                        result += `客户ID: ${clientId}\n`;
                        result += `必选话题: ${JSON.stringify(defaults.required_topics, null, 2)}\n`;
                        result += `随机话题: ${JSON.stringify(defaults.random_topics, null, 2)}\n`;
                        result += `@用户: ${JSON.stringify(defaults.at_users, null, 2)}\n`;
                        result += `定位信息: ${defaults.location || '无'}\n`;
                        
                        resultDiv.textContent = result;
                        resultDiv.style.background = '#e8f5e8';
                        resultDiv.style.color = '#2e7d32';
                    } else {
                        resultDiv.textContent = `❌ API返回错误: ${data.message}`;
                        resultDiv.style.background = '#ffebee';
                        resultDiv.style.color = '#c62828';
                        log(`API返回错误: ${data.message}`);
                    }
                })
                .catch(error => {
                    const errorMsg = `❌ API请求失败: ${error.message}`;
                    resultDiv.textContent = errorMsg;
                    resultDiv.style.background = '#ffebee';
                    resultDiv.style.color = '#c62828';
                    log(`API请求失败: ${error.message}`);
                });
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('客户默认值功能测试页面加载完成');
            log('请按照测试步骤进行功能验证');
        });
    </script>
</body>
</html>
